<!-- Multi-Field Component Template -->
<div [formArrayName]="field.fieldName" class="multi-field-container">
  @for (control of getMultiArray().controls; track control; let i = $index) {
    <div [formGroupName]="i" class="form-field is-multi"
         [class.row-view-multi]="isRowView">

      <!-- Row View Multi-Field Layout -->
      @if (isRowView) {
        <div class="row-view-multi-container">
          <div class="row-view-multi-input">
            @if (field.foreginKey) {
              <app-regular-field
                [field]="field"
                [form]="$any(control)"
                [isViewMode]="isViewMode"
                [fields]="fields"
                [multiIndex]="i + 1"
                (fieldValueChange)="onFieldValueChange($event)">
              </app-regular-field>
            } @else {
              <!-- Regular input fields for non-foreign key multi-fields -->
              @if (field.type === 'string') {
                <input [formControlName]="field.fieldName" type="text"
                  [placeholder]="(field.label?.trim() || field.fieldName) + ' (' + (i + 1) + ')'" 
                  [disabled]="field.noInput" />
              }
              @if (field.type === 'int') {
                <input [formControlName]="field.fieldName" type="number" 
                  [disabled]="field.noInput" />
              }
              @if (field.type === 'boolean') {
                <input [formControlName]="field.fieldName" type="checkbox" 
                  [disabled]="field.noInput" />
              }
              @if (field.type === 'date') {
                <input [formControlName]="field.fieldName" type="date" 
                  [disabled]="field.noInput" />
              }
              @if (field.type === 'double') {
                <input [formControlName]="field.fieldName" type="number" 
                  step="0.01" [disabled]="field.noInput" />
              }
            }
          </div>
          <div class="row-view-multi-buttons">
            @if (getMultiArray().length > 1 && !isViewMode && !field.noInput) {
              <button mat-icon-button color="warn" type="button" 
                (click)="removeMultiField(i)" matTooltip="Delete">
                <mat-icon>delete</mat-icon>
              </button>
            }
            @if (!isViewMode && !field.noInput) {
              <button mat-icon-button color="primary" type="button" 
                (click)="addMultiField(i)" matTooltip="Add">
                <mat-icon>add</mat-icon>
              </button>
            }
          </div>
        </div>
      } @else {
        <!-- Standard Multi-Field Layout -->
        @if (field.foreginKey) {
          <app-regular-field
            [field]="field"
            [form]="$any(control)"
            [isViewMode]="isViewMode"
            [fields]="fields"
            [multiIndex]="i + 1"
            (fieldValueChange)="onFieldValueChange($event)">
          </app-regular-field>
        } @else {
          <div class="multi-input-container">
            <div class="multi-input">
              <label>{{ field.fieldName }} ({{ i + 1 }})
                @if (field.noInput) {<span class="no-input-indicator"> (Read Only)</span>}
              </label>

              <!-- Regular input fields for non-foreign key multi-fields -->
              @if (field.type === 'string') {
                <input [formControlName]="field.fieldName" type="text"
                  [placeholder]="(field.label?.trim() || field.fieldName) + ' (' + (i + 1) + ')'" 
                  [disabled]="field.noInput" />
              }
              @if (field.type === 'int') {
                <input [formControlName]="field.fieldName" type="number" 
                  [disabled]="field.noInput" />
              }
              @if (field.type === 'boolean') {
                <input [formControlName]="field.fieldName" type="checkbox" 
                  [disabled]="field.noInput" />
              }
              @if (field.type === 'date') {
                <input [formControlName]="field.fieldName" type="date" 
                  [disabled]="field.noInput" />
              }
              @if (field.type === 'double') {
                <input [formControlName]="field.fieldName" type="number" 
                  step="0.01" [disabled]="field.noInput" />
              }
            </div>

            <div class="multi-buttons">
              @if (getMultiArray().length > 1 && !isViewMode && !field.noInput) {
                <button mat-icon-button color="warn" type="button" 
                  (click)="removeMultiField(i)" matTooltip="Delete">
                  <mat-icon>delete</mat-icon>
                </button>
              }
              @if (!isViewMode && !field.noInput) {
                <button mat-icon-button color="primary" type="button" 
                  (click)="addMultiField(i)" matTooltip="Add">
                  <mat-icon>add</mat-icon>
                </button>
              }
            </div>
          </div>
        }

        <!-- Add/Remove buttons for foreign key multi-fields -->
        @if (field.foreginKey) {
          <div class="multi-buttons">
            @if (getMultiArray().length > 1 && !isViewMode && !field.noInput) {
              <button mat-icon-button color="warn" type="button" 
                (click)="removeMultiField(i)" matTooltip="Delete">
                <mat-icon>delete</mat-icon>
              </button>
            }
            @if (!isViewMode && !field.noInput) {
              <button mat-icon-button color="primary" type="button" 
                (click)="addMultiField(i)" matTooltip="Add">
                <mat-icon>add</mat-icon>
              </button>
            }
          </div>
        }
      }
    </div>
  }
</div>
