<!-- Multi-Field Component Template -->
<!-- Phase 3: Scenario 1 - Non-grouped multi-field (EXACT COPY from main component) -->

<!-- Scenario 1: Non-Grouped Multi-Field -->
@if (!groupName) {
  <div [formArrayName]="field.fieldName">
    @for (control of getMultiArray(field.fieldName).controls; track control; let j = $index) {
      <div [formGroupName]="j" class="form-field is-multi">
        @if (field.foreginKey) {
          <app-regular-field
            [field]="field"
            [form]="$any(control)"
            [isViewMode]="isViewMode"
            [fields]="fields"
            [multiIndex]="j + 1"
            (fieldValueChange)="onFieldValueChange($event)">
          </app-regular-field>
        } @else {
          <label>{{ field.fieldName }} ({{ j + 1 }})
            @if (field.noInput) {<span class="no-input-indicator"> (Read Only)</span>}
          </label>

          <div class="multi-input-container">
            <div class="multi-input">
              <!-- Regular input fields for non-foreign key multi-fields -->
              @if (field.type === 'string') {
                <input [formControlName]="field.fieldName" type="text"
                  [placeholder]="(field.label?.trim() || field.fieldName) + '-' + (j+1)" [disabled]="field.noInput" />
              }
              @if (field.type === 'int') {
                <input [formControlName]="field.fieldName" type="number" [disabled]="field.noInput" />
              }
              @if (field.type === 'boolean') {
                <input [formControlName]="field.fieldName" type="checkbox" [disabled]="field.noInput" />
              }
              @if (field.type === 'date') {
                <input [formControlName]="field.fieldName" type="date" [disabled]="field.noInput" />
              }
              @if (field.type === 'double') {
                <input [formControlName]="field.fieldName" type="number"
                  step="00.50" [disabled]="field.noInput" />
              }
            </div>

            <div class="multi-buttons">
              @if (getMultiArray(field.fieldName).length > 1 && !isViewMode && !field.noInput) {
                <button mat-icon-button color="warn" type="button" (click)="removeMultiField(field.fieldName, j)" matTooltip="Delete">
                  <mat-icon>delete</mat-icon>
                </button>
              }

              @if (!isViewMode && !field.noInput) {
                <button mat-icon-button color="primary" type="button" (click)="addMultiField(field, undefined, j)" matTooltip="Add">
                  <mat-icon>add</mat-icon>
                </button>
              }
            </div>
          </div>
        }
      </div>
    }
  </div>
}

<!-- Placeholder for remaining scenarios - will be implemented in subsequent steps -->
@if (groupName) {
  <div class="multi-field-placeholder">
    <p>Grouped Multi-Field scenarios will be implemented in next steps</p>
    <p>Group Name: {{ groupName }}</p>
    <p>Is Row View: {{ isRowView }}</p>
    <p>Nested Group Index: {{ nestedGroupIndex }}</p>
  </div>
}
