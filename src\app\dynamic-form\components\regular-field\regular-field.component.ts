import { Component, Input, Output, EventEmitter, On<PERSON><PERSON>roy, OnInit, inject } from '@angular/core';
import { FormGroup } from '@angular/forms';
import { HttpClient } from '@angular/common/http';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule } from '@angular/forms';
import { MatIconModule } from '@angular/material/icon';
import { MatTooltipModule } from '@angular/material/tooltip';
import { environment } from '../../../../environments/environment';

@Component({
  selector: 'app-regular-field',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatIconModule,
    MatTooltipModule
  ],
  templateUrl: './regular-field.component.html',
  styleUrl: './regular-field.component.scss'
})
export class RegularFieldComponent implements OnInit, OnDestroy {
  @Input() field!: any;
  @Input() form!: FormGroup;
  @Input() isViewMode: boolean = false;
  @Input() fields: any[] = []; // Need access to all fields for extractOriginalFieldName
  
  @Output() fieldValueChange = new EventEmitter<{fieldName: string, value: any}>();

  // Dynamic dropdown properties - EXACT from main component lines 85-96
  showTypeDropdown: { [key: string]: boolean } = {};
  filteredTypeOptions: { [key: string]: any[] } = {};
  typeSearchTimeout: { [key: string]: any } = {};

  showForeignKeyDropdown: { [key: string]: boolean } = {};
  filteredForeignKeyOptions: { [key: string]: any[] } = {};
  foreignKeySearchTimeout: { [key: string]: any } = {};

  // Regular dropdown properties (for other foreign keys)
  showRegularDropdown: { [key: string]: boolean } = {};
  filteredRegularOptions: { [key: string]: any[] } = {};
  regularSearchTimeout: { [key: string]: any } = {};

  // Performance optimization: API response cache
  private apiCache: { [key: string]: any[] } = {};

  private http = inject(HttpClient);

  ngOnInit() {
    // Preload all dropdown data for superior performance - matching backup strategy
    this.preloadAllDropdownData();
  }

  ngOnDestroy() {
    // Clear type search timeouts - EXACT from main component lines 112-116
    Object.values(this.typeSearchTimeout).forEach(timeout => {
      if (timeout) {
        clearTimeout(timeout);
      }
    });

    // Clear foreign key search timeouts - EXACT from main component lines 119-123
    Object.values(this.foreignKeySearchTimeout).forEach(timeout => {
      if (timeout) {
        clearTimeout(timeout);
      }
    });

    // Clear regular dropdown search timeouts - EXACT from main component lines 126-130
    Object.values(this.regularSearchTimeout).forEach(timeout => {
      if (timeout) {
        clearTimeout(timeout);
      }
    });
  }

  // Type dropdown methods - EXACT from main component lines 1471-1549
  onTypeInputChange(event: Event, fieldName: string): void {
    const input = event.target as HTMLInputElement;
    const value = input.value;

    // Clear previous timeout
    if (this.typeSearchTimeout[fieldName]) {
      clearTimeout(this.typeSearchTimeout[fieldName]);
    }

    // Set a new timeout to avoid too many API calls
    this.typeSearchTimeout[fieldName] = setTimeout(() => {
      this.searchTypeOptions(value, fieldName);
    }, 300);
  }

  onTypeInputFocus(fieldName: string): void {
    const currentValue = this.form.get(fieldName)?.value || '';
    if (currentValue.trim() === '') {
      this.loadAllTypes(fieldName);
    } else {
      this.searchTypeOptions(currentValue, fieldName);
    }
  }

  onTypeInputBlur(fieldName: string): void {
    // Delay hiding dropdown to allow click on dropdown items
    setTimeout(() => {
      this.showTypeDropdown[fieldName] = false;
    }, 200);
  }

  toggleTypeDropdown(fieldName: string): void {
    if (!this.showTypeDropdown[fieldName]) {
      const currentValue = this.form.get(fieldName)?.value || '';
      if (currentValue.trim() === '') {
        this.loadAllTypes(fieldName);
      } else {
        this.searchTypeOptions(currentValue, fieldName);
      }
    } else {
      this.showTypeDropdown[fieldName] = false;
    }
  }

  searchTypeOptions(searchTerm: string, fieldName: string): void {
    if (searchTerm.trim() === '') {
      this.loadAllTypes(fieldName);
      return;
    }
    // fieldType API doesn't support server-side filtering, use client-side filtering
    this.loadAllAndFilter('fieldType', searchTerm, fieldName, 'type');
  }

  selectTypeOption(option: any, fieldName: string): void {
    this.setDropdownValue(option, fieldName, 'type');
  }

  loadAllTypes(fieldName: string): void {
    const cacheKey = 'fieldType';

    // INSTANT: Use preloaded data for superior performance
    if (this.apiCache[cacheKey]) {
      this.filteredTypeOptions[fieldName] = this.apiCache[cacheKey];
      this.showTypeDropdown[fieldName] = true;
      return;
    }

    // Fallback: Load if not preloaded (should rarely happen)
    const apiUrl = `${environment.baseURL}/api/query-builder/search?queryBuilderId=fieldType`;
    const payload = {
      _select: ["ROW_ID"]
    };

    this.http.post<any[]>(apiUrl, payload, { withCredentials: true }).subscribe({
      next: (response: any) => {
        if (Array.isArray(response)) {
          this.apiCache[cacheKey] = response;
          this.filteredTypeOptions[fieldName] = response;
          this.showTypeDropdown[fieldName] = true;
        } else {
          this.filteredTypeOptions[fieldName] = [];
          this.showTypeDropdown[fieldName] = true;
        }
      },
      error: () => {
        this.filteredTypeOptions[fieldName] = [];
        this.showTypeDropdown[fieldName] = true;
      }
    });
  }

  // Foreign key dropdown methods - EXACT from main component lines 1551-1630
  onForeignKeyInputChange(event: Event, fieldName: string): void {
    const input = event.target as HTMLInputElement;
    const value = input.value;

    // Clear previous timeout
    if (this.foreignKeySearchTimeout[fieldName]) {
      clearTimeout(this.foreignKeySearchTimeout[fieldName]);
    }

    // Set a new timeout to avoid too many API calls
    this.foreignKeySearchTimeout[fieldName] = setTimeout(() => {
      this.searchForeignKeyOptions(value, fieldName);
    }, 300);
  }

  onForeignKeyInputFocus(fieldName: string): void {
    const currentValue = this.form.get(fieldName)?.value || '';
    if (currentValue.trim() === '') {
      this.loadAllForeignKeys(fieldName);
    } else {
      this.searchForeignKeyOptions(currentValue, fieldName);
    }
  }

  onForeignKeyInputBlur(fieldName: string): void {
    // Delay hiding dropdown to allow click on dropdown items
    setTimeout(() => {
      this.showForeignKeyDropdown[fieldName] = false;
    }, 200);
  }

  toggleForeignKeyDropdown(fieldName: string): void {
    if (!this.showForeignKeyDropdown[fieldName]) {
      const currentValue = this.form.get(fieldName)?.value || '';
      if (currentValue.trim() === '') {
        this.loadAllForeignKeys(fieldName);
      } else {
        this.searchForeignKeyOptions(currentValue, fieldName);
      }
    } else {
      this.showForeignKeyDropdown[fieldName] = false;
    }
  }

  searchForeignKeyOptions(searchTerm: string, fieldName: string): void {
    if (searchTerm.trim() === '') {
      this.loadAllForeignKeys(fieldName);
      return;
    }
    // formDefinition API doesn't support server-side filtering, use client-side filtering
    this.loadAllAndFilter('formDefinition', searchTerm, fieldName, 'foreignKey');
  }

  selectForeignKeyOption(option: any, fieldName: string): void {
    this.setDropdownValue(option, fieldName, 'foreignKey');
  }

  loadAllForeignKeys(fieldName: string): void {
    const cacheKey = 'formDefinition';

    // INSTANT: Use preloaded data for superior performance
    if (this.apiCache[cacheKey]) {
      this.filteredForeignKeyOptions[fieldName] = this.apiCache[cacheKey];
      this.showForeignKeyDropdown[fieldName] = true;
      return;
    }

    // Fallback: Load if not preloaded (should rarely happen)
    const apiUrl = `${environment.baseURL}/api/query-builder/search?queryBuilderId=formDefinition`;
    const payload = {
      _select: ["ROW_ID"]
    };

    this.http.post<any[]>(apiUrl, payload, { withCredentials: true }).subscribe({
      next: (response: any) => {
        if (Array.isArray(response)) {
          this.apiCache[cacheKey] = response;
          this.filteredForeignKeyOptions[fieldName] = response;
          this.showForeignKeyDropdown[fieldName] = true;
        } else {
          this.filteredForeignKeyOptions[fieldName] = [];
          this.showForeignKeyDropdown[fieldName] = true;
        }
      },
      error: () => {
        this.filteredForeignKeyOptions[fieldName] = [];
        this.showForeignKeyDropdown[fieldName] = true;
      }
    });
  }

  // Regular dropdown methods (for other foreign keys) - EXACT from main component lines 1632-1722
  onRegularInputChange(event: Event, fieldName: string): void {
    const input = event.target as HTMLInputElement;
    const value = input.value;

    // Clear previous timeout
    if (this.regularSearchTimeout[fieldName]) {
      clearTimeout(this.regularSearchTimeout[fieldName]);
    }

    // Set a new timeout to avoid too many API calls
    this.regularSearchTimeout[fieldName] = setTimeout(() => {
      this.searchRegularOptions(value, fieldName);
    }, 300);
  }

  onRegularInputFocus(fieldName: string): void {
    const currentValue = this.form.get(fieldName)?.value || '';
    if (currentValue.trim() === '') {
      this.loadAllRegularOptions(fieldName);
    } else {
      this.searchRegularOptions(currentValue, fieldName);
    }
  }

  onRegularInputBlur(fieldName: string): void {
    // Delay hiding dropdown to allow click on dropdown items
    setTimeout(() => {
      this.showRegularDropdown[fieldName] = false;
    }, 200);
  }

  toggleRegularDropdown(fieldName: string): void {
    if (!this.showRegularDropdown[fieldName]) {
      const currentValue = this.form.get(fieldName)?.value || '';
      if (currentValue.trim() === '') {
        this.loadAllRegularOptions(fieldName);
      } else {
        this.searchRegularOptions(currentValue, fieldName);
      }
    } else {
      this.showRegularDropdown[fieldName] = false;
    }
  }

  searchRegularOptions(searchTerm: string, fieldName: string): void {
    // Extract the original field name from complex field names
    const originalFieldName = this.extractOriginalFieldName(fieldName);
    const field = this.fields.find(f => f.fieldName === originalFieldName);
    if (!field || !field.foreginKey) return;

    if (searchTerm.trim() === '') {
      this.loadAllRegularOptions(fieldName);
      return;
    }

    // Use client-side filtering for regular foreign keys
    this.loadAllAndFilterRegular(field.foreginKey, searchTerm, fieldName);
  }

  selectRegularOption(option: any, fieldName: string): void {
    this.setDropdownValue(option, fieldName, 'regular');
  }

  loadAllRegularOptions(fieldName: string): void {
    // Extract the original field name from complex field names
    const originalFieldName = this.extractOriginalFieldName(fieldName);
    const field = this.fields.find(f => f.fieldName === originalFieldName);
    if (!field || !field.foreginKey) return;

    const cacheKey = field.foreginKey;

    // INSTANT: Use preloaded data for superior performance
    if (this.apiCache[cacheKey]) {
      this.filteredRegularOptions[fieldName] = this.apiCache[cacheKey];
      this.showRegularDropdown[fieldName] = true;
      return;
    }

    // Fallback: Load if not preloaded (should rarely happen)
    const apiUrl = `${environment.baseURL}/api/query-builder/search?queryBuilderId=${field.foreginKey}`;
    const payload = {
      _select: ["ROW_ID"]
    };

    this.http.post<any[]>(apiUrl, payload, { withCredentials: true }).subscribe({
      next: (response: any) => {
        if (Array.isArray(response)) {
          this.apiCache[cacheKey] = response;
          this.filteredRegularOptions[fieldName] = response;
          this.showRegularDropdown[fieldName] = true;
        } else {
          this.filteredRegularOptions[fieldName] = [];
          this.showRegularDropdown[fieldName] = true;
        }
      },
      error: () => {
        this.filteredRegularOptions[fieldName] = [];
        this.showRegularDropdown[fieldName] = true;
      }
    });
  }

  // Helper methods - EXACT from main component lines 1358-1393, 1724-1769
  private loadAllAndFilter(queryBuilderId: string, searchTerm: string, fieldName: string, dropdownType: 'type' | 'foreignKey'): void {
    const cacheKey = queryBuilderId;

    // INSTANT: Use preloaded data for superior performance
    if (this.apiCache[cacheKey]) {
      const filtered = this.apiCache[cacheKey].filter(option =>
        option.ROW_ID.toLowerCase().includes(searchTerm.toLowerCase())
      );
      if (dropdownType === 'type') {
        this.filteredTypeOptions[fieldName] = filtered;
        this.showTypeDropdown[fieldName] = true;
      } else {
        this.filteredForeignKeyOptions[fieldName] = filtered;
        this.showForeignKeyDropdown[fieldName] = true;
      }
      return;
    }

    // Fallback: Load if not preloaded (should rarely happen)
    const apiUrl = `${environment.baseURL}/api/query-builder/search?queryBuilderId=${queryBuilderId}`;
    const payload = { _select: ["ROW_ID"] };

    this.http.post<any[]>(apiUrl, payload, { withCredentials: true }).subscribe({
      next: (response: any) => {
        if (Array.isArray(response)) {
          this.apiCache[cacheKey] = response;
          const filtered = response.filter(option =>
            option.ROW_ID.toLowerCase().includes(searchTerm.toLowerCase())
          );
          if (dropdownType === 'type') {
            this.filteredTypeOptions[fieldName] = filtered;
            this.showTypeDropdown[fieldName] = true;
          } else {
            this.filteredForeignKeyOptions[fieldName] = filtered;
            this.showForeignKeyDropdown[fieldName] = true;
          }
        } else {
          this.setEmptyDropdownState(fieldName, dropdownType);
        }
      },
      error: () => {
        this.setEmptyDropdownState(fieldName, dropdownType);
      }
    });
  }

  private setEmptyDropdownState(fieldName: string, dropdownType: 'type' | 'foreignKey'): void {
    if (dropdownType === 'type') {
      this.filteredTypeOptions[fieldName] = [];
      this.showTypeDropdown[fieldName] = true;
    } else {
      this.filteredForeignKeyOptions[fieldName] = [];
      this.showForeignKeyDropdown[fieldName] = true;
    }
  }

  private setDropdownValue(option: any, fieldName: string, dropdownType: 'type' | 'foreignKey' | 'regular'): void {
    // Get the form control and set the value
    const formControl = this.form.get(fieldName);

    if (formControl) {
      formControl.setValue(option.ROW_ID);
    }

    // Close the appropriate dropdown
    if (dropdownType === 'type') {
      this.showTypeDropdown[fieldName] = false;
    } else if (dropdownType === 'foreignKey') {
      this.showForeignKeyDropdown[fieldName] = false;
    } else if (dropdownType === 'regular') {
      this.showRegularDropdown[fieldName] = false;
    }

    // Emit the change event
    this.fieldValueChange.emit({ fieldName, value: option.ROW_ID });
  }

  /**
   * Extract the original field name from complex field names like:
   * - fieldName_group_k
   * - fieldName_nested_k_n
   * - fieldName_group_k_multi_l
   * - fieldName_j (for multi-fields)
   * EXACT from main component lines 1724-1745
   */
  private extractOriginalFieldName(fieldName: string): string {
    if (fieldName.includes('_nested_')) {
      return fieldName.split('_nested_')[0];
    } else if (fieldName.includes('_group_')) {
      return fieldName.split('_group_')[0];
    } else if (fieldName.includes('_')) {
      // Handle simple multi-field pattern like fieldName_j
      const parts = fieldName.split('_');
      if (parts.length === 2 && !isNaN(parseInt(parts[1]))) {
        return parts[0];
      }
      return fieldName;
    }
    return fieldName;
  }

  private loadAllAndFilterRegular(queryBuilderId: string, searchTerm: string, fieldName: string): void {
    const cacheKey = queryBuilderId;

    // INSTANT: Use preloaded data for superior performance
    if (this.apiCache[cacheKey]) {
      const filtered = this.apiCache[cacheKey].filter(option =>
        option.ROW_ID.toLowerCase().includes(searchTerm.toLowerCase())
      );
      this.filteredRegularOptions[fieldName] = filtered;
      this.showRegularDropdown[fieldName] = true;
      return;
    }

    // Fallback: Load if not preloaded (should rarely happen)
    const apiUrl = `${environment.baseURL}/api/query-builder/search?queryBuilderId=${queryBuilderId}`;
    const payload = { _select: ["ROW_ID"] };

    this.http.post<any[]>(apiUrl, payload, { withCredentials: true }).subscribe({
      next: (response: any) => {
        if (Array.isArray(response)) {
          this.apiCache[cacheKey] = response;
          const filtered = response.filter(option =>
            option.ROW_ID.toLowerCase().includes(searchTerm.toLowerCase())
          );
          this.filteredRegularOptions[fieldName] = filtered;
          this.showRegularDropdown[fieldName] = true;
        } else {
          this.filteredRegularOptions[fieldName] = [];
          this.showRegularDropdown[fieldName] = true;
        }
      },
      error: () => {
        this.filteredRegularOptions[fieldName] = [];
        this.showRegularDropdown[fieldName] = true;
      }
    });
  }

  // Utility method for displaying dropdown options - EXACT from main component line 1292
  getKeys(option: any): string[] {
    return Object.keys(option);
  }

  // Performance optimization: trackBy functions - EXACT from backup line 768
  trackByOptionId(_index: number, option: any): string {
    return option.ROW_ID;
  }

  trackByKey(_index: number, key: string): string {
    return key;
  }

  // High-performance preloading strategy - EXACT from backup implementation approach
  private preloadAllDropdownData(): void {
    // Preload fieldType data (used by Type dropdowns)
    this.preloadDropdownData('fieldType');

    // Preload formDefinition data (used by Foreign Key dropdowns)
    this.preloadDropdownData('formDefinition');

    // Preload all unique foreign key dropdown data from fields
    if (this.fields && this.fields.length > 0) {
      const uniqueForeignKeys = new Set<string>();
      this.fields.forEach(field => {
        if (field.foreginKey && field.foreginKey !== 'fieldType' && field.foreginKey !== 'formDefinition') {
          uniqueForeignKeys.add(field.foreginKey);
        }
      });

      // Preload each unique foreign key
      uniqueForeignKeys.forEach(foreignKey => {
        this.preloadDropdownData(foreignKey);
      });
    }
  }

  private preloadDropdownData(queryBuilderId: string): void {
    const cacheKey = queryBuilderId;

    // Skip if already cached
    if (this.apiCache[cacheKey]) {
      return;
    }

    const apiUrl = `${environment.baseURL}/api/query-builder/search?queryBuilderId=${queryBuilderId}`;
    const payload = { _select: ["ROW_ID"] };

    this.http.post<any[]>(apiUrl, payload, { withCredentials: true }).subscribe({
      next: (response: any) => {
        if (Array.isArray(response)) {
          this.apiCache[cacheKey] = response;
        }
      },
      error: () => {
        // Handle preload error silently
      }
    });
  }
}
