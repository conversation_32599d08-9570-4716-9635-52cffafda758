import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, ReactiveFormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { InitialInputComponent } from '../dynamic-form/components/initial-input/initial-input.component';

@Component({
  selector: 'app-test-initial-input',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    InitialInputComponent
  ],
  template: `
    <div style="padding: 20px;">
      <h2>Test Initial Input Component</h2>
      <app-initial-input 
        [form]="testForm"
        [tableName]="'test_table'"
        [screenName]="'test_screen'"
        [showValidation]="false"
        (loadDataAndBuildForm)="onLoadDataAndBuildForm()"
        (viewData)="onViewData()"
        (validationChange)="onValidationChange($event)">
      </app-initial-input>
      
      <div style="margin-top: 20px;">
        <h3>Form Value:</h3>
        <pre>{{ testForm.value | json }}</pre>
      </div>
    </div>
  `
})
export class TestInitialInputComponent implements OnInit {
  testForm!: FormGroup;

  constructor(private fb: FormBuilder) {}

  ngOnInit() {
    this.testForm = this.fb.group({
      ID: ['']
    });
  }

  onLoadDataAndBuildForm() {
    console.log('loadDataAndBuildForm event received');
  }

  onViewData() {
    console.log('viewData event received');
  }

  onValidationChange(showValidation: boolean) {
    console.log('validationChange event received:', showValidation);
  }
}
