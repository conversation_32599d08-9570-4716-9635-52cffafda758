import { Component, Input, Output, EventEmitter, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, FormArray, Validators } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule } from '@angular/forms';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatTooltipModule } from '@angular/material/tooltip';
import { RegularFieldComponent } from '../regular-field/regular-field.component';

@Component({
  selector: 'app-multi-field',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatIconModule,
    MatButtonModule,
    MatTooltipModule,
    RegularFieldComponent
  ],
  templateUrl: './multi-field.component.html',
  styleUrls: ['./multi-field.component.scss']
})
export class MultiFieldComponent implements OnInit {
  @Input() field: any;
  @Input() form!: FormGroup;
  @Input() isViewMode: boolean = false;
  @Input() fields: any[] = [];
  @Input() isRowView: boolean = false;
  @Input() groupIndex?: number;
  @Input() groupName?: string;
  @Input() nestedGroupIndex?: number;

  @Output() fieldValueChange = new EventEmitter<any>();

  constructor(private fb: FormBuilder) {}

  ngOnInit() {
    // Component initialization
  }

  /**
   * Get the FormArray for multi-field - matches backup implementation exactly
   */
  getMultiArray(): FormArray {
    if (this.groupIndex !== undefined && this.groupName) {
      // Check if this is a nested group
      const parsed = this.parseGroupPath(this.groupName);
      if (parsed.isNested && parsed.parent && parsed.child && this.nestedGroupIndex !== undefined) {
        // Navigate to nested group: parent[groupIndex].child[nestedGroupIndex].fieldName
        const parentArray = this.getGroupArray(parsed.parent);
        const parentGroup = parentArray.at(this.groupIndex) as FormGroup;
        const childArray = parentGroup.get(parsed.child) as FormArray;
        const childGroup = childArray.at(this.nestedGroupIndex) as FormGroup;
        return childGroup.get(this.field.fieldName) as FormArray;
      } else {
        // Regular group
        const groupArray = this.getGroupArray(this.groupName);
        const group = groupArray.at(this.groupIndex) as FormGroup;
        return group.get(this.field.fieldName) as FormArray;
      }
    } else {
      return this.form.get(this.field.fieldName) as FormArray;
    }
  }

  /**
   * Helper method to get group array
   */
  private getGroupArray(groupName: string): FormArray {
    return this.form.get(groupName) as FormArray;
  }

  /**
   * Parse group path for nested groups
   */
  private parseGroupPath(groupPath: string): { parent: string | null, child: string | null, isNested: boolean } {
    if (groupPath && groupPath.includes('|')) {
      const parts = groupPath.split('|');
      return {
        parent: parts[0],
        child: parts[1],
        isNested: true
      };
    }
    return {
      parent: groupPath,
      child: null,
      isNested: false
    };
  }

  /**
   * Create a new multi-field FormGroup - matches backup implementation exactly
   */
  createMultiField(): FormGroup {
    const group = this.fb.group({});
    if (Array.isArray(this.field)) {
      this.field.forEach(fieldName => {
        const control = this.fb.control('', Validators.required);
        group.addControl(fieldName, control);
      });
    } else {
      const control = this.fb.control("", this.field.mandatory ? Validators.required : null);
      group.addControl(this.field.fieldName, control);
      // Disable control if noInput is true
      if (this.field.noInput) {
        control.disable({ emitEvent: false });
      }
    }
    return group;
  }

  /**
   * Add a new multi-field instance - matches backup implementation exactly
   */
  addMultiField(index?: number) {
    try {
      const multiArray = this.getMultiArray();
      const newField = this.createMultiField();
      if (index !== undefined) {
        multiArray.insert(index + 1, newField);
      } else {
        multiArray.push(newField);
      }
    } catch (error) {
      // Handle error silently
    }
  }

  /**
   * Remove a multi-field instance - matches backup implementation exactly
   */
  removeMultiField(index: number) {
    const multiArray = this.getMultiArray();
    multiArray.removeAt(index);
  }

  /**
   * Handle field value changes
   */
  onFieldValueChange(event: any) {
    this.fieldValueChange.emit(event);
  }
}
