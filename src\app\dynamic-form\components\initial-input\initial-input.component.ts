import { Component, Input, Output, EventEmitter, OnInit, OnDestroy, inject } from '@angular/core';
import { FormGroup, ReactiveFormsModule } from '@angular/forms';
import { HttpClient } from '@angular/common/http';
import { CommonModule } from '@angular/common';
import { environment } from '../../../../environments/environment';

// Angular Material imports
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatTooltipModule } from '@angular/material/tooltip';

@Component({
  selector: 'app-initial-input',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatButtonModule,
    MatIconModule,
    MatTooltipModule
  ],
  templateUrl: './initial-input.component.html',
  styleUrl: './initial-input.component.scss'
})
export class InitialInputComponent implements OnInit, OnDestroy {
  @Input() form!: FormGroup;
  @Input() tableName!: string;
  @Input() screenName!: string;
  @Input() showValidation: boolean = false;

  @Output() loadDataAndBuildForm = new EventEmitter<void>();
  @Output() viewData = new EventEmitter<void>();
  @Output() validationChange = new EventEmitter<boolean>();

  // ID dropdown properties
  showIdDropdown = false;
  idOptions: any[] = [];
  filteredIdOptions: any[] = [];
  idSearchTimeout: any;

  private http = inject(HttpClient);

  ngOnInit() {
    console.log('InitialInputComponent initialized');
    console.log('Form:', this.form);
    console.log('tableName:', this.tableName);
    console.log('screenName:', this.screenName);
    console.log('showValidation:', this.showValidation);
  }

  ngOnDestroy() {
    // Clear any pending timeouts
    if (this.idSearchTimeout) {
      clearTimeout(this.idSearchTimeout);
    }
  }

  onIdInputChange(event: Event): void {
    console.log('onIdInputChange called');
    const input = event.target as HTMLInputElement;
    const value = input.value;
    console.log('Input value:', value);

    if (value && value.trim() !== '') {
      this.showValidation = false;
      this.validationChange.emit(false);
    }

    // Clear previous timeout
    if (this.idSearchTimeout) {
      clearTimeout(this.idSearchTimeout);
    }

    // Set a new timeout to avoid too many API calls
    this.idSearchTimeout = setTimeout(() => {
      console.log('Calling searchIdOptions with:', value);
      this.searchIdOptions(value);
    }, 300);
  }

  getInputClass(): string {
    return this.showValidation ? 'invalid-input' : '';
  }

  onIdInputFocus(): void {
    console.log('onIdInputFocus called');
    const currentValue = this.form.get('ID')?.value || '';
    console.log('Current value on focus:', currentValue);
    if (currentValue.trim() === '') {
      console.log('Loading all IDs');
      this.loadAllIds();
    } else {
      console.log('Searching for:', currentValue);
      this.searchIdOptions(currentValue);
    }
  }

  onIdInputBlur(): void {
    // Delay hiding dropdown to allow click on dropdown items
    setTimeout(() => {
      this.showIdDropdown = false;
    }, 200);
  }

  toggleIdDropdown(): void {
    console.log('toggleIdDropdown called, current showIdDropdown:', this.showIdDropdown);
    if (!this.showIdDropdown) {
      const currentValue = this.form.get('ID')?.value || '';
      console.log('Toggling dropdown, current value:', currentValue);
      // If no value is entered, show all IDs
      if (currentValue.trim() === '') {
        console.log('Loading all IDs from toggle');
        this.loadAllIds();
      } else {
        console.log('Searching from toggle:', currentValue);
        this.searchIdOptions(currentValue);
      }
    } else {
      console.log('Hiding dropdown');
      this.showIdDropdown = false;
    }
  }

  // Helper method to extract part before comma for query-builder API calls
  private extractQueryBuilderId(tableName?: string): string {
    const nameToUse = tableName || this.screenName || this.tableName;
    if (nameToUse && nameToUse.includes(',')) {
      return nameToUse.split(',')[0].trim();
    }
    return nameToUse;
  }

  searchIdOptions(searchTerm: string): void {
    console.log('searchIdOptions called with:', searchTerm);
    console.log('tableName:', this.tableName, 'screenName:', this.screenName);

    if (!this.tableName && !this.screenName) {
      console.log('No tableName or screenName, returning');
      return;
    }

    const queryBuilderId = this.extractQueryBuilderId();
    console.log('queryBuilderId:', queryBuilderId);
    const apiUrl = `${environment.baseURL}/api/query-builder/search?queryBuilderId=${queryBuilderId}`;
    console.log('API URL:', apiUrl);

    const payload = {
      ID: {
        CT: searchTerm // CT = Contains operator
      },
      _select: ["ID"],
      _limit: 20
    };
    console.log('Payload:', payload);

    this.http.post<any[]>(apiUrl, payload, { withCredentials: true }).subscribe({
      next: (response: any) => {
        console.log('API Response:', response);
        if (Array.isArray(response)) {
          this.filteredIdOptions = response;
          this.showIdDropdown = true;
          console.log('Set filteredIdOptions:', this.filteredIdOptions, 'showIdDropdown:', this.showIdDropdown);
        } else {
          this.filteredIdOptions = [];
          this.showIdDropdown = true; // Show dropdown with "No IDs found" message
          console.log('Response not array, set empty options');
        }
      },
      error: (error) => {
        console.log('API Error:', error);
        this.filteredIdOptions = [];
        this.showIdDropdown = true; // Show dropdown with "No IDs found" message
      }
    });
  }

  selectIdOption(option: any): void {
    this.form.get('ID')?.setValue(option.ID);
    this.showIdDropdown = false;
    // Reset validation state when a valid ID is selected
    this.showValidation = false;
    this.validationChange.emit(false);
  }

  loadAllIds(): void {
    if (!this.tableName && !this.screenName) {
      return;
    }

    // Use the query-builder API to get all IDs
    const queryBuilderId = this.extractQueryBuilderId();
    const apiUrl = `${environment.baseURL}/api/query-builder/search?queryBuilderId=${queryBuilderId}`;
    const payload = {
      _select: ["ID"],
      _limit: 100 // Higher limit to get more IDs
    };

    this.http.post<any[]>(apiUrl, payload, { withCredentials: true }).subscribe({
      next: (response: any) => {
        if (Array.isArray(response)) {
          this.filteredIdOptions = response;
          this.showIdDropdown = true;
        } else {
          this.filteredIdOptions = [];
          this.showIdDropdown = true; // Show dropdown even if no data to show "No IDs found"
        }
      },
      error: (error) => {
        this.filteredIdOptions = [];
        this.showIdDropdown = true;
      }
    });
  }

  onAddClick(): void {
    console.log('Add button clicked');
    this.loadDataAndBuildForm.emit();
  }

  onEditClick(): void {
    console.log('Edit button clicked');
    this.loadDataAndBuildForm.emit();
  }

  onViewClick(): void {
    console.log('View button clicked');
    this.viewData.emit();
  }

  onMaintenanceClick(): void {
    // Placeholder for maintenance functionality
    console.log('Maintenance clicked');
  }
}
