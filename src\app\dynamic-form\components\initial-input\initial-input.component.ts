import { Component, Input, Output, EventEmitter, OnDestroy, inject } from '@angular/core';
import { FormGroup, ReactiveFormsModule } from '@angular/forms';
import { HttpClient } from '@angular/common/http';
import { CommonModule } from '@angular/common';
import { environment } from '../../../../environments/environment';

// Angular Material imports
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatTooltipModule } from '@angular/material/tooltip';

@Component({
  selector: 'app-initial-input',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatButtonModule,
    MatIconModule,
    MatTooltipModule
  ],
  templateUrl: './initial-input.component.html',
  styleUrl: './initial-input.component.scss'
})
export class InitialInputComponent implements OnDestroy {
  @Input() form!: FormGroup;
  @Input() tableName!: string;
  @Input() screenName!: string;
  @Input() showValidation: boolean = false;
  
  @Output() loadDataAndBuildForm = new EventEmitter<void>();
  @Output() viewData = new EventEmitter<void>();
  @Output() validationChange = new EventEmitter<boolean>();

  // ID dropdown properties
  showIdDropdown = false;
  idOptions: any[] = [];
  filteredIdOptions: any[] = [];
  idSearchTimeout: any;

  private http = inject(HttpClient);

  ngOnDestroy() {
    // Clear any pending timeouts
    if (this.idSearchTimeout) {
      clearTimeout(this.idSearchTimeout);
    }
  }

  onIdInputChange(event: Event): void {
    const input = event.target as HTMLInputElement;
    const value = input.value;

    if (value && value.trim() !== '') {
      this.showValidation = false;
      this.validationChange.emit(false);
    }

    // Clear previous timeout
    if (this.idSearchTimeout) {
      clearTimeout(this.idSearchTimeout);
    }

    // Set a new timeout to avoid too many API calls
    this.idSearchTimeout = setTimeout(() => {
      this.searchIdOptions(value);
    }, 300);
  }

  getInputClass(): string {
    return this.showValidation ? 'invalid-input' : '';
  }

  onIdInputFocus(): void {
    const currentValue = this.form.get('ID')?.value || '';
    if (currentValue.trim() === '') {
      this.loadAllIds();
    } else {
      this.searchIdOptions(currentValue);
    }
  }

  onIdInputBlur(): void {
    // Delay hiding dropdown to allow click on dropdown items
    setTimeout(() => {
      this.showIdDropdown = false;
    }, 200);
  }

  toggleIdDropdown(): void {
    if (!this.showIdDropdown) {
      const currentValue = this.form.get('ID')?.value || '';
      // If no value is entered, show all IDs
      if (currentValue.trim() === '') {
        this.loadAllIds();
      } else {
        this.searchIdOptions(currentValue);
      }
    } else {
      this.showIdDropdown = false;
    }
  }

  // Helper method to extract part before comma for query-builder API calls
  private extractQueryBuilderId(tableName?: string): string {
    const nameToUse = tableName || this.screenName || this.tableName;
    if (nameToUse && nameToUse.includes(',')) {
      return nameToUse.split(',')[0].trim();
    }
    return nameToUse;
  }

  searchIdOptions(searchTerm: string): void {
    if (!this.tableName && !this.screenName) {
      return;
    }

    const queryBuilderId = this.extractQueryBuilderId();
    const apiUrl = `${environment.baseURL}/api/query-builder/search?queryBuilderId=${queryBuilderId}`;
    const payload = {
      ID: {
        CT: searchTerm // CT = Contains operator
      },
      _select: ["ID"],
      _limit: 20
    };

    this.http.post<any[]>(apiUrl, payload, { withCredentials: true }).subscribe({
      next: (response: any) => {
        if (Array.isArray(response)) {
          this.filteredIdOptions = response;
          this.showIdDropdown = true;
        } else {
          this.filteredIdOptions = [];
          this.showIdDropdown = true; // Show dropdown with "No IDs found" message
        }
      },
      error: (error) => {
        this.filteredIdOptions = [];
        this.showIdDropdown = true; // Show dropdown with "No IDs found" message
      }
    });
  }

  selectIdOption(option: any): void {
    this.form.get('ID')?.setValue(option.ID);
    this.showIdDropdown = false;
    // Reset validation state when a valid ID is selected
    this.showValidation = false;
    this.validationChange.emit(false);
  }

  loadAllIds(): void {
    if (!this.tableName && !this.screenName) {
      return;
    }

    // Use the query-builder API to get all IDs
    const queryBuilderId = this.extractQueryBuilderId();
    const apiUrl = `${environment.baseURL}/api/query-builder/search?queryBuilderId=${queryBuilderId}`;
    const payload = {
      _select: ["ID"],
      _limit: 100 // Higher limit to get more IDs
    };

    this.http.post<any[]>(apiUrl, payload, { withCredentials: true }).subscribe({
      next: (response: any) => {
        if (Array.isArray(response)) {
          this.filteredIdOptions = response;
          this.showIdDropdown = true;
        } else {
          this.filteredIdOptions = [];
          this.showIdDropdown = true; // Show dropdown even if no data to show "No IDs found"
        }
      },
      error: (error) => {
        this.filteredIdOptions = [];
        this.showIdDropdown = true;
      }
    });
  }

  onAddClick(): void {
    this.loadDataAndBuildForm.emit();
  }

  onEditClick(): void {
    this.loadDataAndBuildForm.emit();
  }

  onViewClick(): void {
    this.viewData.emit();
  }

  onMaintenanceClick(): void {
    // Placeholder for maintenance functionality
    console.log('Maintenance clicked');
  }
}
