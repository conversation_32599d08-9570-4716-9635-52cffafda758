@use '../../../../styles/shared.scss' as *;

.form-container {
  display: flex;
  flex-direction: column;
  gap: 16px;
  width: 100%;
  background-color: white;
}

.form-main-field {
  display: flex;
  align-items: center;
  gap: 24px;
  width: 100%;
  padding-top: 16px;
  background-color: white;
}

.form-label {
  width: 160px;
  font-family: 'Poppins', sans-serif;
  font-weight: 500;
  font-size: 18px;
  color: #000000;
  padding-left: 24px;
}

.input-button-group {
  display: flex;
  align-items: center;
  gap: 16px;
  flex-grow: 1;
  padding-right: 24px;
}

.form-input {
  flex-grow: 1;
  height: 40px;
  border: 1px solid #DBDBDB;
  border-radius: 8px;
  font-family: 'Poppins', sans-serif;
  font-size: 16px;
  color: #222222;
  background: #FFFFFF;
  transition: all 0.3s ease;
  padding: 0 12px;
  
  &:focus {
    outline: none;
    border-color: #9e9e9e;
    box-shadow: 0 0 0 2px rgba(158, 158, 158, 0.25);
  }
  
  &.invalid-input {
    border-color: #f44336;
    background-color: rgba(244, 67, 54, 0.05);
    
    &:focus {
      box-shadow: 0 0 0 2px rgba(244, 67, 54, 0.2);
    }
  }
  
  &::placeholder {
    color: #999;
  }
}

/* ID Input Container with Dropdown */
.id-input-container {
  position: relative;
  display: flex;
  align-items: center;
  flex-grow: 1;
}

.id-input-container .form-input {
  flex-grow: 1;
  border-radius: 8px 0 0 8px;
  border-right: none;
}

.dropdown-arrow-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border: 1px solid #DBDBDB;
  border-left: none;
  border-radius: 0 8px 8px 0;
  background-color: #f8f9fa;
  color: #495057;
  cursor: pointer;
  transition: all 0.3s ease;
  padding: 0;
  margin: 0;
}

.dropdown-arrow-btn:hover {
  background-color: #e9ecef;
  color: #283A97;
}

.dropdown-arrow-btn:focus {
  outline: none;
  box-shadow: 0 0 0 0.2rem rgba(158, 158, 158, 0.25);
}

.dropdown-arrow-btn .mat-icon {
  font-size: 20px;
  width: 20px;
  height: 20px;
  line-height: 1;
}

/* ID Dropdown List */
.id-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  right: 40px; /* Account for arrow button width */
  z-index: 1000;
  background-color: white;
  border: 1px solid #DBDBDB;
  border-top: none;
  border-radius: 0 0 8px 8px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  max-height: 200px;
  overflow-y: auto;
}

.id-dropdown-item {
  padding: 12px 16px;
  cursor: pointer;
  border-bottom: 1px solid #f1f3f4;
  font-family: 'Poppins', sans-serif;
  font-size: 14px;
  color: #495057;
  transition: background-color 0.2s ease;
}

.id-dropdown-item:hover {
  background-color: #f8f9fa;
  color: #283A97;
}

.id-dropdown-item:last-child {
  border-bottom: none;
}

/* Empty state for dropdown */
.id-dropdown-empty {
  padding: 12px 16px;
  color: #6c757d;
  font-style: italic;
  text-align: center;
}

/* Scrollbar styling for dropdown */
.id-dropdown::-webkit-scrollbar {
  width: 6px;
}

.id-dropdown::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.id-dropdown::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.id-dropdown::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Enhanced Initial Input Button Styles */
.initial-input-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 44px;
  height: 44px;
  border: 2px solid;
  border-radius: 8px;
  font-family: 'Poppins', sans-serif;
  cursor: pointer;
  transition: all 0.3s ease;
  padding: 0;
  margin: 0;
  min-width: 44px;
  min-height: 44px;
  position: relative;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  
  mat-icon {
    font-size: 20px;
    width: 20px;
    height: 20px;
  }
}

/* Add Button - Green theme */
.initial-input-button.add-button {
  border-color: #4CAF50;
  background-color: white;
  color: #4CAF50;
  
  &:hover {
    background-color: #4CAF50;
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(76, 175, 80, 0.3);
  }
}

/* Edit Button - Blue theme */
.initial-input-button.edit-button {
  border-color: #2196F3;
  background-color: white;
  color: #2196F3;
  
  &:hover {
    background-color: #2196F3;
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(33, 150, 243, 0.3);
  }
}

/* View Button - Orange theme */
.initial-input-button.view-button {
  border-color: #FF9800;
  background-color: white;
  color: #FF9800;
  
  &:hover {
    background-color: #FF9800;
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(255, 152, 0, 0.3);
  }
}

/* Maintenance Button - Red theme */
.initial-input-button.maintenance-button {
  border-color: #F44336;
  background-color: white;
  color: #F44336;
  
  &:hover {
    background-color: #F44336;
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(244, 67, 54, 0.3);
  }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .form-main-field {
    flex-direction: column;
    align-items: stretch;
    gap: 16px;
  }
  
  .form-label {
    width: 100%;
    padding-left: 0;
    text-align: center;
  }
  
  .input-button-group {
    padding-right: 0;
    justify-content: center;
    flex-wrap: wrap;
  }
  
  .initial-input-button {
    width: 40px;
    height: 40px;
    min-width: 40px;
    min-height: 40px;
  }
  
  .dropdown-arrow-btn {
    width: 36px;
    height: 36px;
  }
  
  .dropdown-arrow-btn .mat-icon {
    font-size: 18px;
    width: 18px;
    height: 18px;
  }
  
  .id-dropdown {
    right: 36px;
  }
  
  .id-dropdown-item {
    padding: 10px 12px;
    font-size: 13px;
  }
}

@media (max-width: 480px) {
  .initial-input-button {
    width: 36px;
    height: 36px;
    min-width: 36px;
    min-height: 36px;
  }
  
  .dropdown-arrow-btn {
    width: 32px;
    height: 32px;
  }
  
  .dropdown-arrow-btn .mat-icon {
    font-size: 16px;
    width: 16px;
    height: 16px;
  }
  
  .id-dropdown {
    right: 32px;
  }
  
  .id-dropdown-item {
    padding: 8px 10px;
    font-size: 12px;
  }
}
