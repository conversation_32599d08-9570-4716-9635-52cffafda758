@use '../../../../styles/shared.scss' as *;

.regular-field {
  width: 100%;
}

.form-field {
  display: flex;
  flex-direction: column;
  margin-bottom: 16px;
  width: 100%;
}

.form-label {
  font-family: 'Poppins', sans-serif;
  font-weight: 500;
  font-size: 14px;
  color: #333;
  margin-bottom: 8px;
  
  span {
    color: #f44336;
    font-weight: 600;
  }
  
  .no-input-indicator {
    color: #666;
    font-size: 12px;
    font-style: italic;
  }
}

/* Dropdown input container */
.dropdown-input-container {
  position: relative;
  display: flex;
  align-items: center;
  width: 100%;
}

/* Form inputs */
.form-input {
  width: 100%;
  padding: 12px 16px;
  border: 2px solid #e0e0e0;
  border-radius: 8px;
  font-family: 'Poppins', sans-serif;
  font-size: 14px;
  color: #333;
  background-color: #fff;
  transition: all 0.3s ease;
  box-sizing: border-box;
  
  &:focus {
    outline: none;
    border-color: #2196F3;
    box-shadow: 0 0 0 3px rgba(33, 150, 243, 0.1);
  }
  
  &:disabled,
  &[readonly] {
    background-color: #f5f5f5;
    color: #666;
    cursor: not-allowed;
  }
  
  &::placeholder {
    color: #999;
    font-style: italic;
  }
}

.dropdown-input {
  padding-right: 48px; // Make room for arrow button
}

/* Dropdown arrow button */
.dropdown-arrow-btn {
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s ease;
  
  &:hover {
    background-color: rgba(0, 0, 0, 0.04);
  }
  
  &:disabled {
    cursor: not-allowed;
    opacity: 0.5;
  }
  
  mat-icon {
    font-size: 20px;
    width: 20px;
    height: 20px;
    color: #666;
  }
}

/* Dropdown list */
.dropdown-list {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  max-height: 200px;
  overflow-y: auto;
  z-index: 1000;
  margin-top: 4px;
}

.dropdown-item {
  padding: 12px 16px;
  cursor: pointer;
  border-bottom: 1px solid #f0f0f0;
  font-family: 'Poppins', sans-serif;
  font-size: 14px;
  color: #333;
  transition: background-color 0.2s ease;
  
  &:hover {
    background-color: #f5f5f5;
  }
  
  &:last-child {
    border-bottom: none;
  }
}

.dropdown-empty {
  padding: 12px 16px;
  color: #999;
  font-style: italic;
  text-align: center;
  font-family: 'Poppins', sans-serif;
  font-size: 14px;
}

/* Checkbox styling */
input[type="checkbox"] {
  width: 20px;
  height: 20px;
  cursor: pointer;
  accent-color: #2196F3;
  
  &:disabled {
    cursor: not-allowed;
    opacity: 0.5;
  }
}

/* Number input styling */
input[type="number"] {
  -moz-appearance: textfield;
  
  &::-webkit-outer-spin-button,
  &::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
  }
}

/* Date input styling */
input[type="date"] {
  &::-webkit-calendar-picker-indicator {
    cursor: pointer;
    filter: invert(0.6);
  }
  
  &:disabled::-webkit-calendar-picker-indicator {
    cursor: not-allowed;
    opacity: 0.5;
  }
}

/* Validation states */
.form-field.has-error {
  .form-input {
    border-color: #f44336;
    
    &:focus {
      border-color: #f44336;
      box-shadow: 0 0 0 3px rgba(244, 67, 54, 0.1);
    }
  }
}

.form-field.has-success {
  .form-input {
    border-color: #4caf50;
    
    &:focus {
      border-color: #4caf50;
      box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.1);
    }
  }
}

/* Responsive design */
@media (max-width: 768px) {
  .form-input {
    padding: 10px 14px;
    font-size: 16px; // Prevent zoom on iOS
  }
  
  .dropdown-input {
    padding-right: 44px;
  }
  
  .dropdown-arrow-btn {
    right: 6px;
    
    mat-icon {
      font-size: 18px;
      width: 18px;
      height: 18px;
    }
  }
  
  .dropdown-item {
    padding: 10px 14px;
    font-size: 14px;
  }
}

@media (max-width: 480px) {
  .form-field {
    margin-bottom: 12px;
  }
  
  .form-input {
    padding: 8px 12px;
  }
  
  .dropdown-input {
    padding-right: 40px;
  }
  
  .dropdown-arrow-btn {
    right: 4px;
    
    mat-icon {
      font-size: 16px;
      width: 16px;
      height: 16px;
    }
  }
  
  .dropdown-item {
    padding: 8px 12px;
    font-size: 13px;
  }
  
  .dropdown-list {
    max-height: 150px;
  }
}
