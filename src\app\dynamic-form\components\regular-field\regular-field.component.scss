// Exact styles from backup dynamic-form.component.scss

/* Form Fields */
.form-field {
  width: 100%;
  margin-bottom: 16px;
  position: relative;
  display: flex;
  flex-direction: column;
}

.form-field label {
  display: block;
  margin-bottom: 5px;
  font-weight: 500;
  font-size: 12px;
  color: #000000;
}

.form-field input,
.form-field select {
  width: 100%;
  max-width: 100%;
  padding: 8px 12px;
  font-size: 14px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  background-color: #fff;
  box-sizing: border-box;
}

.form-field input:focus,
.form-field select:focus {
  border-color: #9e9e9e;
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(158, 158, 158, 0.25);
}

.form-input {
  flex-grow: 1;
  height: 40px;
  border: 1px solid #DBDBDB;
  border-radius: 8px;
  font-family: 'Poppins', sans-serif;
  font-size: 14px;
  color: #495057;
  background-color: #fff;
  transition: all 0.3s ease;
  padding: 8px 12px;
  box-sizing: border-box;
}

.form-input:focus {
  border-color: #9e9e9e;
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(158, 158, 158, 0.25);
}

/* Dynamic Dropdown Styles for Type and Foreign Key Fields */
.dropdown-input-container {
  position: relative;
  display: flex;
  align-items: center;
  width: 100%;
}

.dropdown-input-container .form-input {
  flex-grow: 1;
  border-radius: 8px 0 0 8px;
  border-right: none;
  padding: 8px 12px;
}

.dropdown-input-container .dropdown-arrow-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background-color: #f8f9fa;
  border: 1px solid #DBDBDB;
  border-left: none;
  border-radius: 0 8px 8px 0;
  cursor: pointer;
  color: #495057;
  transition: all 0.3s ease;
  padding: 0;
  margin: 0;
}

.dropdown-input-container .dropdown-arrow-btn:hover {
  background-color: #e9ecef;
  color: #283A97;
}

.dropdown-input-container .dropdown-arrow-btn:focus {
  outline: none;
  box-shadow: 0 0 0 0.2rem rgba(158, 158, 158, 0.25);
}

.dropdown-input-container .dropdown-arrow-btn .mat-icon {
  font-size: 20px;
  width: 20px;
  height: 20px;
  line-height: 1;
}

/* Dropdown List Styles */
.dropdown-list {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  z-index: 1000;
  background-color: #fff;
  border: 1px solid #DBDBDB;
  border-radius: 8px;
  margin-top: 2px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  max-height: 200px;
  overflow-y: auto;
}

.dropdown-item {
  padding: 12px 16px;
  cursor: pointer;
  border-bottom: 1px solid #f1f3f4;
  font-family: 'Poppins', sans-serif;
  font-size: 14px;
  color: #495057;
  transition: background-color 0.2s ease;
}

.dropdown-item:hover {
  background-color: #f8f9fa;
  color: #283A97;
}

.dropdown-item:last-child {
  border-bottom: none;
}

.dropdown-empty {
  padding: 12px 16px;
  color: #6c757d;
  font-style: italic;
  text-align: center;
}

/* Checkbox styling */
input[type="checkbox"] {
  vertical-align: middle;
  width: auto !important;
  flex-shrink: 0;
}

.form-field:has(input[type="checkbox"]) {
  display: flex !important;
  flex-direction: row !important;
  align-items: center !important;
  gap: 8px !important;
}

.form-field:has(input[type="checkbox"]) label {
  margin-bottom: 0 !important;
  display: flex !important;
  align-items: center !important;
  gap: 8px !important;
  order: 2;
}

.form-field:has(input[type="checkbox"]) input[type="checkbox"] {
  order: 1;
  margin-right: 8px;
}

/* Required field indicator styling - targeting the span element */
.form-field label span {
  color: #dc3545;
  font-weight: bold;
  margin-left: 2px;
}

/* No input indicator styling */
.no-input-indicator {
  color: #6c757d;
  font-size: 11px;
  font-style: italic;
  font-weight: normal;
}

/* Responsive styling */
@media (max-width: 768px) {
  .form-field input,
  .form-field select {
    font-size: 16px; /* Prevent zoom on iOS */
    padding: 10px 12px;
  }

  .dropdown-input-container .dropdown-arrow-btn {
    width: 36px;
    height: 36px;
  }

  .dropdown-input-container .dropdown-arrow-btn .mat-icon {
    font-size: 18px;
    width: 18px;
    height: 18px;
  }

  .dropdown-item {
    padding: 10px 12px;
    font-size: 13px;
  }

  .dropdown-empty {
    padding: 10px 12px;
    font-size: 13px;
  }

  .form-field:has(input[type="checkbox"]) {
    flex-direction: row !important;
    align-items: center !important;
    gap: 6px !important;
  }

  input[type="checkbox"] {
    margin-right: 6px;
  }
}

@media (max-width: 480px) {
  .form-field input,
  .form-field select {
    padding: 8px 10px;
    font-size: 14px;
  }

  .dropdown-input-container .dropdown-arrow-btn {
    width: 32px;
    height: 32px;
  }

  .dropdown-input-container .dropdown-arrow-btn .mat-icon {
    font-size: 16px;
    width: 16px;
    height: 16px;
  }

  .dropdown-item {
    padding: 8px 10px;
    font-size: 12px;
  }

  .dropdown-empty {
    padding: 8px 10px;
    font-size: 12px;
  }

  .form-field:has(input[type="checkbox"]) {
    gap: 4px !important;
  }

  input[type="checkbox"] {
    margin-right: 4px;
  }
}

/* Scrollbar styling for dropdown lists */
.dropdown-list::-webkit-scrollbar {
  width: 6px;
}

.dropdown-list::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.dropdown-list::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.dropdown-list::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
