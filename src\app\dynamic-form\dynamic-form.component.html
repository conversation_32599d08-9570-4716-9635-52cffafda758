@if (showSuccessPopup) {
  <div class="popup">
    <div class="popup-content">
      <span class="close" (click)="closeSuccessPopup()">&times;</span>
      <p>Record Inserted Successfully</p>
    </div>
  </div>
}

@if (!submissionSuccess) {
  @if (showInitialInput) {
    <app-initial-input
      [form]="form"
      [tableName]="tableName"
      [screenName]="screenName"
      [showValidation]="showValidation"
      (loadDataAndBuildForm)="loadDataAndBuildForm()"
      (viewData)="viewData()"
      (validationChange)="onValidationChange($event)">
    </app-initial-input>
  }

  @if (errorMessage) {
    <div class="error-message">{{ errorMessage }}</div>
  }

  @if (!showInitialInput) {
    <div class="form-grid">
    <form [formGroup]="form" (ngSubmit)="onSubmit()">
      <app-form-header
        [form]="form"
        [isViewMode]="isViewMode"
        [isRowView]="isRowView"
        [errorMessage]="errorMessage"
        (toggleViewMode)="toggleViewMode()"
        (submitForm)="onSubmit()"
        (validateRecord)="validateRecord()"
        (authorizeRecord)="authorizeRecord()"
        (goBack)="goBack()"
        (rejectRecord)="onRejectRecord()"
        (deleteRecord)="onDeleteRecord()">
      </app-form-header>
      <!-- 🔹 All Fields in Order (Unified Rendering) -->
      <div class="form-grid" [ngClass]="'columns-' + columnCount">
        <div class="form-column" *ngFor="let column of columns">
          <ng-container *ngFor="let field of column">
          <!-- Skip ID field as it's handled separately -->
          @if (field.fieldName?.toUpperCase() !== 'ID') {

            <!-- 🔸 Non-Grouped Fields -->
            @if (!field.Group) {
              <div class="form-field">

                <!-- 🔸 Regular Field -->
                @if (!field.isMulti) {
                  <app-regular-field
                    [field]="field"
                    [form]="form"
                    [fields]="fields"
                    [isViewMode]="isViewMode"
                    (fieldValueChange)="onFieldValueChange($event)">
                  </app-regular-field>
                }

                <!-- 🔸 Multi-Field (Non-Grouped) -->
                @if (field.isMulti) {
                  <div [formArrayName]="field.fieldName">
                    @for (control of getMultiArray(field.fieldName).controls; track control; let j = $index) {
                      <div [formGroupName]="j" class="form-field is-multi">
                        <label>{{ field.fieldName }} ({{ j + 1 }})
                          @if (field.noInput) {<span class="no-input-indicator"> (Read Only)</span>}
                        </label>

                        <div class="multi-input-container">
                          <div class="multi-input">
                            @if (field.foreginKey) {
                              <!-- Check if this is a type field (fieldType) -->
                              @if (field.foreginKey === 'fieldType') {
                                <div class="dropdown-input-container">
                                  <input [formControlName]="field.fieldName" type="text" 
                                         class="form-input dropdown-input" 
                                         (input)="onTypeInputChange($event, field.fieldName + '_' + j)" 
                                         (focus)="onTypeInputFocus(field.fieldName + '_' + j)" 
                                         (blur)="onTypeInputBlur(field.fieldName + '_' + j)"
                                         [disabled]="field.noInput"
                                         [placeholder]="'Search ' + (field.label?.trim() || field.fieldName)" />
                                  
                                  <button type="button" class="dropdown-arrow-btn" 
                                          (click)="toggleTypeDropdown(field.fieldName + '_' + j)" 
                                          [disabled]="field.noInput"
                                          matTooltip="Show type suggestions">
                                    <mat-icon>{{ showTypeDropdown[field.fieldName + '_' + j] ? 'keyboard_arrow_up' : 'keyboard_arrow_down' }}</mat-icon>
                                  </button>
                                  
                                  @if (showTypeDropdown[field.fieldName + '_' + j]) {
                                    <div class="dropdown-list">
                                      @if (filteredTypeOptions[field.fieldName + '_' + j] && filteredTypeOptions[field.fieldName + '_' + j].length > 0) {
                                        @for (option of filteredTypeOptions[field.fieldName + '_' + j]; track option.ROW_ID) {
                                          <div class="dropdown-item" (click)="selectTypeOption(option, field.fieldName + '_' + j)">
                                            {{ option.ROW_ID }}
                                          </div>
                                        }
                                      } @else {
                                        <div class="dropdown-empty">
                                          No types found
                                        </div>
                                      }
                                    </div>
                                  }
                                </div>
                              }
                              <!-- Check if this is a foreign key field (formDefinition) -->
                              @else if (field.foreginKey === 'formDefinition') {
                                <div class="dropdown-input-container">
                                  <input [formControlName]="field.fieldName" type="text" 
                                         class="form-input dropdown-input" 
                                         (input)="onForeignKeyInputChange($event, field.fieldName + '_' + j)" 
                                         (focus)="onForeignKeyInputFocus(field.fieldName + '_' + j)" 
                                         (blur)="onForeignKeyInputBlur(field.fieldName + '_' + j)"
                                         [disabled]="field.noInput"
                                         [placeholder]="'Search ' + (field.label?.trim() || field.fieldName)" />
                                  
                                  <button type="button" class="dropdown-arrow-btn" 
                                          (click)="toggleForeignKeyDropdown(field.fieldName + '_' + j)" 
                                          [disabled]="field.noInput"
                                          matTooltip="Show foreign key suggestions">
                                    <mat-icon>{{ showForeignKeyDropdown[field.fieldName + '_' + j] ? 'keyboard_arrow_up' : 'keyboard_arrow_down' }}</mat-icon>
                                  </button>
                                  
                                  @if (showForeignKeyDropdown[field.fieldName + '_' + j]) {
                                    <div class="dropdown-list">
                                      @if (filteredForeignKeyOptions[field.fieldName + '_' + j] && filteredForeignKeyOptions[field.fieldName + '_' + j].length > 0) {
                                        @for (option of filteredForeignKeyOptions[field.fieldName + '_' + j]; track option.ROW_ID) {
                                          <div class="dropdown-item" (click)="selectForeignKeyOption(option, field.fieldName + '_' + j)">
                                            {{ option.ROW_ID }}
                                          </div>
                                        }
                                      } @else {
                                        <div class="dropdown-empty">
                                          No foreign keys found
                                        </div>
                                      }
                                    </div>
                                  }
                                </div>
                              }
                              <!-- Default dropdown for other foreign keys -->
                              @else {
                                <div class="dropdown-input-container">
                                  <input [formControlName]="field.fieldName" type="text" 
                                         class="form-input dropdown-input" 
                                         (input)="onRegularInputChange($event, field.fieldName + '_' + j)" 
                                         (focus)="onRegularInputFocus(field.fieldName + '_' + j)" 
                                         (blur)="onRegularInputBlur(field.fieldName + '_' + j)"
                                         [disabled]="field.noInput"
                                         [placeholder]="'Search ' + (field.label?.trim() || field.fieldName)" />
                                  
                                  <button type="button" class="dropdown-arrow-btn" 
                                          (click)="toggleRegularDropdown(field.fieldName + '_' + j)" 
                                          [disabled]="field.noInput"
                                          matTooltip="Show options">
                                    <mat-icon>{{ showRegularDropdown[field.fieldName + '_' + j] ? 'keyboard_arrow_up' : 'keyboard_arrow_down' }}</mat-icon>
                                  </button>
                                  
                                  @if (showRegularDropdown[field.fieldName + '_' + j]) {
                                    <div class="dropdown-list">
                                      @if (filteredRegularOptions[field.fieldName + '_' + j] && filteredRegularOptions[field.fieldName + '_' + j].length > 0) {
                                        @for (option of filteredRegularOptions[field.fieldName + '_' + j]; track option.ROW_ID) {
                                          <div class="dropdown-item" (click)="selectRegularOption(option, field.fieldName + '_' + j)">
                                            @for (key of getKeys(option); track key) {
                                              {{ option[key] }}&nbsp;
                                            }
                                          </div>
                                        }
                                      } @else {
                                        <div class="dropdown-empty">
                                          No options found
                                        </div>
                                      }
                                    </div>
                                  }
                                </div>
                              }
                            } @else {
                              <!-- Regular input fields for non-foreign key multi-fields -->
                              @if (field.type === 'string') {
                                <input [formControlName]="field.fieldName" type="text"
                                  [placeholder]="(field.label?.trim() || field.fieldName) + '-' + (j+1)" [disabled]="field.noInput" />
                              }
                              @if (field.type === 'int') {
                                <input [formControlName]="field.fieldName" type="number" [disabled]="field.noInput" />
                              }
                              @if (field.type === 'boolean') {
                                <input [formControlName]="field.fieldName" type="checkbox" [disabled]="field.noInput" />
                              }
                              @if (field.type === 'date') {
                                <input [formControlName]="field.fieldName" type="date" [disabled]="field.noInput" />
                              }
                              @if (field.type === 'double') {
                                <input [formControlName]="field.fieldName" type="number"
                                  step="00.50" [disabled]="field.noInput" />
                              }
                            }
                          </div>

                          <div class="multi-buttons">
                            @if (getMultiArray(field.fieldName).length > 1 && !isViewMode && !field.noInput) {
                              <button mat-icon-button color="warn" type="button" (click)="removeMultiField(field.fieldName, j)" matTooltip="Delete">
                                <mat-icon>delete</mat-icon>
                              </button>
                            }

                            @if (!isViewMode && !field.noInput) {
                              <button mat-icon-button color="primary" type="button" (click)="addMultiField(field, undefined, j)" matTooltip="Add">
                                <mat-icon>add</mat-icon>
                              </button>
                            }
                          </div>
                        </div>
                      </div>
                    }
                  </div>
                }

              </div>
            }

            <!-- 🔸 Grouped Fields -->
            @if (field.Group && isFirstFieldInParentGroup(field)) {
              @let parsed = parseGroupPath(field.Group);
              @if (parsed.parent) {
                <div [formArrayName]="parsed.parent" class="grouped-field-section">
                  <h3>{{ parsed.parent }}</h3>
                  @for (group of getGroupArray(parsed.parent).controls; track group; let k = $index) {
                    <div [formGroupName]="k" [class]="isRowView ? 'form-grid row-view-table' : 'form-grid multi-field'">

                      @if (isRowView) {
                        <!-- Row View: All fields in a single table-like row -->
                        <div class="row-view-table-container">
                          <!-- Parent group fields -->
                          @for (groupField of getFieldsForGroup(parsed.parent); track groupField.fieldName) {
                            @if (!groupField.isMulti) {
                              <div class="row-view-table-cell">
                                <label>{{ groupField.fieldName }} @if (groupField.mandatory) {<span>*</span>}
                                  @if (groupField.noInput) {<span class="no-input-indicator"> (Read Only)</span>}
                                </label>
                                @if (groupField.foreginKey) {
                                  <!-- Check if this is a type field (fieldType) -->
                                  @if (groupField.foreginKey === 'fieldType') {
                                    <div class="dropdown-input-container">
                                      <input [formControlName]="groupField.fieldName" type="text"
                                             class="form-input dropdown-input"
                                             (input)="onTypeInputChange($event, groupField.fieldName + '_group_' + k)"
                                             (focus)="onTypeInputFocus(groupField.fieldName + '_group_' + k)"
                                             (blur)="onTypeInputBlur(groupField.fieldName + '_group_' + k)"
                                             [disabled]="groupField.noInput"
                                             [placeholder]="'Search ' + (groupField.label?.trim() || groupField.fieldName)" />

                                      <button type="button" class="dropdown-arrow-btn"
                                              (click)="toggleTypeDropdown(groupField.fieldName + '_group_' + k)"
                                              [disabled]="groupField.noInput"
                                              matTooltip="Show type suggestions">
                                        <mat-icon>{{ showTypeDropdown[groupField.fieldName + '_group_' + k] ? 'keyboard_arrow_up' : 'keyboard_arrow_down' }}</mat-icon>
                                      </button>

                                      @if (showTypeDropdown[groupField.fieldName + '_group_' + k]) {
                                        <div class="dropdown-list">
                                          @if (filteredTypeOptions[groupField.fieldName + '_group_' + k] && filteredTypeOptions[groupField.fieldName + '_group_' + k].length > 0) {
                                            @for (option of filteredTypeOptions[groupField.fieldName + '_group_' + k]; track option.ROW_ID) {
                                              <div class="dropdown-item" (click)="selectTypeOption(option, groupField.fieldName + '_group_' + k)">
                                                {{ option.ROW_ID }}
                                              </div>
                                            }
                                          } @else {
                                            <div class="dropdown-empty">
                                              No types found
                                            </div>
                                          }
                                        </div>
                                      }
                                    </div>
                                  }
                                  
                                  <!-- Check if this is a foreign key field (formDefinition) -->
                                  @else if (groupField.foreginKey === 'formDefinition') {
                                    <div class="dropdown-input-container">
                                      <input [formControlName]="groupField.fieldName" type="text"
                                             class="form-input dropdown-input"
                                             (input)="onForeignKeyInputChange($event, groupField.fieldName + '_group_' + k)"
                                             (focus)="onForeignKeyInputFocus(groupField.fieldName + '_group_' + k)"
                                             (blur)="onForeignKeyInputBlur(groupField.fieldName + '_group_' + k)"
                                             [disabled]="groupField.noInput"
                                             [placeholder]="'Search ' + (groupField.label?.trim() || groupField.fieldName)" />

                                      <button type="button" class="dropdown-arrow-btn"
                                              (click)="toggleForeignKeyDropdown(groupField.fieldName + '_group_' + k)"
                                              [disabled]="groupField.noInput"
                                              matTooltip="Show foreign key suggestions">
                                        <mat-icon>{{ showForeignKeyDropdown[groupField.fieldName + '_group_' + k] ? 'keyboard_arrow_up' : 'keyboard_arrow_down' }}</mat-icon>
                                      </button>

                                      @if (showForeignKeyDropdown[groupField.fieldName + '_group_' + k]) {
                                        <div class="dropdown-list">
                                          @if (filteredForeignKeyOptions[groupField.fieldName + '_group_' + k] && filteredForeignKeyOptions[groupField.fieldName + '_group_' + k].length > 0) {
                                            @for (option of filteredForeignKeyOptions[groupField.fieldName + '_group_' + k]; track option.ROW_ID) {
                                              <div class="dropdown-item" (click)="selectForeignKeyOption(option, groupField.fieldName + '_group_' + k)">
                                                {{ option.ROW_ID }}
                                              </div>
                                            }
                                          } @else {
                                            <div class="dropdown-empty">
                                              No foreign keys found
                                            </div>
                                          }
                                        </div>
                                      }
                                    </div>
                                  }
                                  <!-- Default dropdown for other foreign keys -->
                                  @else {
                                    <div class="dropdown-input-container">
                                      <input [formControlName]="groupField.fieldName" type="text"
                                             class="form-input dropdown-input"
                                             (input)="onRegularInputChange($event, groupField.fieldName + '_group_' + k)"
                                             (focus)="onRegularInputFocus(groupField.fieldName + '_group_' + k)"
                                             (blur)="onRegularInputBlur(groupField.fieldName + '_group_' + k)"
                                             [disabled]="groupField.noInput"
                                             [placeholder]="'Search ' + (groupField.label?.trim() || groupField.fieldName)" />

                                      <button type="button" class="dropdown-arrow-btn"
                                              (click)="toggleRegularDropdown(groupField.fieldName + '_group_' + k)"
                                              [disabled]="groupField.noInput"
                                              matTooltip="Show options">
                                        <mat-icon>{{ showRegularDropdown[groupField.fieldName + '_group_' + k] ? 'keyboard_arrow_up' : 'keyboard_arrow_down' }}</mat-icon>
                                      </button>

                                      @if (showRegularDropdown[groupField.fieldName + '_group_' + k]) {
                                        <div class="dropdown-list">
                                          @if (filteredRegularOptions[groupField.fieldName + '_group_' + k] && filteredRegularOptions[groupField.fieldName + '_group_' + k].length > 0) {
                                            @for (option of filteredRegularOptions[groupField.fieldName + '_group_' + k]; track option.ROW_ID) {
                                              <div class="dropdown-item" (click)="selectRegularOption(option, groupField.fieldName + '_group_' + k)">
                                                @for (key of getKeys(option); track key) {
                                                  {{ option[key] }}&nbsp;
                                                }
                                              </div>
                                            }
                                          } @else {
                                            <div class="dropdown-empty">
                                              No options found
                                            </div>
                                          }
                                        </div>
                                      }
                                    </div>
                                  }
                                } @else {
                                  <!-- Regular input fields for non-foreign key grouped fields -->
                                  @if (groupField.type === 'string') {
                                    <input [formControlName]="groupField.fieldName" type="text" [placeholder]="(groupField.label?.trim() || groupField.fieldName)" [disabled]="groupField.noInput" />
                                  }
                                  @if (groupField.type === 'int') {
                                    <input [formControlName]="groupField.fieldName" type="number" [disabled]="groupField.noInput" />
                                  }
                                  @if (groupField.type === 'boolean') {
                                    <input [formControlName]="groupField.fieldName" type="checkbox" [disabled]="groupField.noInput" />
                                  }
                                  @if (groupField.type === 'date') {
                                    <input [formControlName]="groupField.fieldName" type="date" [disabled]="groupField.noInput" />
                                  }
                                  @if (groupField.type === 'double') {
                                    <input [formControlName]="groupField.fieldName" type="number" step="00.50" [disabled]="groupField.noInput" />
                                  }
                                }
                              </div>
                            }

                            <!-- Multi-fields for parent group -->
                            @if (groupField.isMulti) {
                              <div class="row-view-table-cell-multi" [formArrayName]="groupField.fieldName">
                                <label>{{ groupField.fieldName }}
                                  @if (groupField.noInput) {<span class="no-input-indicator"> (Read Only)</span>}
                                </label>
                                @for (multiControl of getMultiArray(groupField.fieldName, k, field.Group).controls; track multiControl; let l = $index) {
                                  <div [formGroupName]="l" class="row-view-multi-item">
                                    <div class="row-view-multi-input">
                                      @if (groupField.type === 'string') {
                                        <input [formControlName]="groupField.fieldName" type="text"
                                          [placeholder]="(groupField.label?.trim() || groupField.fieldName) + ' (' + (l + 1) + ')'" [disabled]="groupField.noInput" />
                                      }
                                      @if (groupField.type === 'int') {
                                        <input [formControlName]="groupField.fieldName" type="number" [disabled]="groupField.noInput" />
                                      }
                                      @if (groupField.type === 'boolean') {
                                        <input [formControlName]="groupField.fieldName" type="checkbox" [disabled]="groupField.noInput" />
                                      }
                                      @if (groupField.type === 'date') {
                                        <input [formControlName]="groupField.fieldName" type="date" [disabled]="groupField.noInput" />
                                      }
                                      @if (groupField.type === 'double') {
                                        <input [formControlName]="groupField.fieldName" type="number" step="0.01" [disabled]="groupField.noInput" />
                                      }
                                      @if (groupField.foreginKey) {
                                        <div class="dropdown-input-container">
                                          <input [formControlName]="groupField.fieldName" type="text"
                                                 class="form-input dropdown-input"
                                                 (input)="onRegularInputChange($event, groupField.fieldName + '_group_' + k + '_multi_' + l)"
                                                 (focus)="onRegularInputFocus(groupField.fieldName + '_group_' + k + '_multi_' + l)"
                                                 (blur)="onRegularInputBlur(groupField.fieldName + '_group_' + k + '_multi_' + l)"
                                                 [disabled]="groupField.noInput"
                                                 [placeholder]="'Search ' + (groupField.label?.trim() || groupField.fieldName)" />

                                          <button type="button" class="dropdown-arrow-btn"
                                                  (click)="toggleRegularDropdown(groupField.fieldName + '_group_' + k + '_multi_' + l)"
                                                  [disabled]="groupField.noInput"
                                                  matTooltip="Show options">
                                            <mat-icon>{{ showRegularDropdown[groupField.fieldName + '_group_' + k + '_multi_' + l] ? 'keyboard_arrow_up' : 'keyboard_arrow_down' }}</mat-icon>
                                          </button>

                                          @if (showRegularDropdown[groupField.fieldName + '_group_' + k + '_multi_' + l]) {
                                            <div class="dropdown-list">
                                              @if (filteredRegularOptions[groupField.fieldName + '_group_' + k + '_multi_' + l] && filteredRegularOptions[groupField.fieldName + '_group_' + k + '_multi_' + l].length > 0) {
                                                @for (option of filteredRegularOptions[groupField.fieldName + '_group_' + k + '_multi_' + l]; track option.ROW_ID) {
                                                  <div class="dropdown-item" (click)="selectRegularOption(option, groupField.fieldName + '_group_' + k + '_multi_' + l)">
                                                    @for (key of getKeys(option); track key) {
                                                      {{ option[key] }}&nbsp;
                                                    }
                                                  </div>
                                                }
                                              } @else {
                                                <div class="dropdown-empty">
                                                  No options found
                                                </div>
                                              }
                                            </div>
                                          }
                                        </div>
                                      }
                                    </div>
                                    <div class="row-view-multi-buttons">
                                      @if (getMultiArray(groupField.fieldName, k, field.Group).controls.length > 1 && !isViewMode && !groupField.noInput) {
                                        <button mat-icon-button color="warn" type="button" (click)="removeMultiField(groupField.fieldName, l, k, field.Group)" matTooltip="Delete">
                                          <mat-icon>delete</mat-icon>
                                        </button>
                                      }
                                      @if (!isViewMode && !groupField.noInput) {
                                        <button mat-icon-button color="primary" type="button" (click)="addMultiField(groupField, k, l, field.Group)" matTooltip="Add">
                                          <mat-icon>add</mat-icon>
                                        </button>
                                      }
                                    </div>
                                  </div>
                                }
                              </div>
                            }
                          }

                          <!-- Nested group fields -->
                          @for (childGroup of getChildGroups(parsed.parent); track childGroup) {
                            @for (nestedGroup of getNestedGroupArray(parsed.parent + '|' + childGroup, k).controls; track nestedGroup; let n = $index) {
                              @for (nestedField of getFieldsForGroupPath(parsed.parent + '|' + childGroup); track nestedField.fieldName) {
                                @if (!nestedField.isMulti) {
                                  <div class="row-view-table-cell" [formArrayName]="childGroup" [formGroupName]="n">
                                    <label>{{ nestedField.fieldName }} @if (nestedField.mandatory) {<span>*</span>}
                                      @if (nestedField.noInput) {<span class="no-input-indicator"> (Read Only)</span>}
                                    </label>
                                    @if (nestedField.foreginKey) {
                                      <!-- Check if this is a type field (fieldType) -->
                                      @if (nestedField.foreginKey === 'fieldType') {
                                        <div class="dropdown-input-container">
                                          <input [formControlName]="nestedField.fieldName" type="text"
                                                 class="form-input dropdown-input"
                                                 (input)="onTypeInputChange($event, nestedField.fieldName + '_nested_' + k + '_' + n)"
                                                 (focus)="onTypeInputFocus(nestedField.fieldName + '_nested_' + k + '_' + n)"
                                                 (blur)="onTypeInputBlur(nestedField.fieldName + '_nested_' + k + '_' + n)"
                                                 [disabled]="nestedField.noInput"
                                                 [placeholder]="'Search ' + (nestedField.label?.trim() || nestedField.fieldName)" />

                                          <button type="button" class="dropdown-arrow-btn"
                                                  (click)="toggleTypeDropdown(nestedField.fieldName + '_nested_' + k + '_' + n)"
                                                  [disabled]="nestedField.noInput"
                                                  matTooltip="Show type suggestions">
                                            <mat-icon>{{ showTypeDropdown[nestedField.fieldName + '_nested_' + k + '_' + n] ? 'keyboard_arrow_up' : 'keyboard_arrow_down' }}</mat-icon>
                                          </button>

                                          @if (showTypeDropdown[nestedField.fieldName + '_nested_' + k + '_' + n]) {
                                            <div class="dropdown-list">
                                              @if (filteredTypeOptions[nestedField.fieldName + '_nested_' + k + '_' + n] && filteredTypeOptions[nestedField.fieldName + '_nested_' + k + '_' + n].length > 0) {
                                                @for (option of filteredTypeOptions[nestedField.fieldName + '_nested_' + k + '_' + n]; track option.ROW_ID) {
                                                  <div class="dropdown-item" (click)="selectTypeOption(option, nestedField.fieldName + '_nested_' + k + '_' + n)">
                                                    {{ option.ROW_ID }}
                                                  </div>
                                                }
                                              } @else {
                                                <div class="dropdown-empty">
                                                  No types found
                                                </div>
                                              }
                                            </div>
                                          }
                                        </div>
                                      }
                                      <!-- Check if this is a foreign key field (formDefinition) -->
                                      @else if (nestedField.foreginKey === 'formDefinition') {
                                        <div class="dropdown-input-container">
                                          <input [formControlName]="nestedField.fieldName" type="text"
                                                 class="form-input dropdown-input"
                                                 (input)="onForeignKeyInputChange($event, nestedField.fieldName + '_nested_' + k + '_' + n)"
                                                 (focus)="onForeignKeyInputFocus(nestedField.fieldName + '_nested_' + k + '_' + n)"
                                                 (blur)="onForeignKeyInputBlur(nestedField.fieldName + '_nested_' + k + '_' + n)"
                                                 [disabled]="nestedField.noInput"
                                                 [placeholder]="'Search ' + (nestedField.label?.trim() || nestedField.fieldName)" />

                                          <button type="button" class="dropdown-arrow-btn"
                                                  (click)="toggleForeignKeyDropdown(nestedField.fieldName + '_nested_' + k + '_' + n)"
                                                  [disabled]="nestedField.noInput"
                                                  matTooltip="Show foreign key suggestions">
                                            <mat-icon>{{ showForeignKeyDropdown[nestedField.fieldName + '_nested_' + k + '_' + n] ? 'keyboard_arrow_up' : 'keyboard_arrow_down' }}</mat-icon>
                                          </button>

                                          @if (showForeignKeyDropdown[nestedField.fieldName + '_nested_' + k + '_' + n]) {
                                            <div class="dropdown-list">
                                              @if (filteredForeignKeyOptions[nestedField.fieldName + '_nested_' + k + '_' + n] && filteredForeignKeyOptions[nestedField.fieldName + '_nested_' + k + '_' + n].length > 0) {
                                                @for (option of filteredForeignKeyOptions[nestedField.fieldName + '_nested_' + k + '_' + n]; track option.ROW_ID) {
                                                  <div class="dropdown-item" (click)="selectForeignKeyOption(option, nestedField.fieldName + '_nested_' + k + '_' + n)">
                                                    {{ option.ROW_ID }}
                                                  </div>
                                                }
                                              } @else {
                                                <div class="dropdown-empty">
                                                  No foreign keys found
                                                </div>
                                              }
                                            </div>
                                          }
                                        </div>
                                      }
                                      <!-- Default dropdown for other foreign keys -->
                                      @else {
                                        <div class="dropdown-input-container">
                                          <input [formControlName]="nestedField.fieldName" type="text"
                                                 class="form-input dropdown-input"
                                                 (input)="onRegularInputChange($event, nestedField.fieldName + '_nested_' + k + '_' + n)"
                                                 (focus)="onRegularInputFocus(nestedField.fieldName + '_nested_' + k + '_' + n)"
                                                 (blur)="onRegularInputBlur(nestedField.fieldName + '_nested_' + k + '_' + n)"
                                                 [disabled]="nestedField.noInput"
                                                 [placeholder]="'Search ' + (nestedField.label?.trim() || nestedField.fieldName)" />

                                          <button type="button" class="dropdown-arrow-btn"
                                                  (click)="toggleRegularDropdown(nestedField.fieldName + '_nested_' + k + '_' + n)"
                                                  [disabled]="nestedField.noInput"
                                                  matTooltip="Show options">
                                            <mat-icon>{{ showRegularDropdown[nestedField.fieldName + '_nested_' + k + '_' + n] ? 'keyboard_arrow_up' : 'keyboard_arrow_down' }}</mat-icon>
                                          </button>

                                          @if (showRegularDropdown[nestedField.fieldName + '_nested_' + k + '_' + n]) {
                                            <div class="dropdown-list">
                                              @if (filteredRegularOptions[nestedField.fieldName + '_nested_' + k + '_' + n] && filteredRegularOptions[nestedField.fieldName + '_nested_' + k + '_' + n].length > 0) {
                                                @for (option of filteredRegularOptions[nestedField.fieldName + '_nested_' + k + '_' + n]; track option.ROW_ID) {
                                                  <div class="dropdown-item" (click)="selectRegularOption(option, nestedField.fieldName + '_nested_' + k + '_' + n)">
                                                    @for (key of getKeys(option); track key) {
                                                      {{ option[key] }}&nbsp;
                                                    }
                                                  </div>
                                                }
                                              } @else {
                                                <div class="dropdown-empty">
                                                  No options found
                                                </div>
                                              }
                                            </div>
                                          }
                                        </div>
                                      }
                                    } @else {
                                      <!-- Regular input fields for non-foreign key nested fields -->
                                      @if (nestedField.type === 'string') {
                                        <input [formControlName]="nestedField.fieldName" type="text" [placeholder]="(nestedField.label?.trim() || nestedField.fieldName)" [disabled]="nestedField.noInput" />
                                      }
                                      @if (nestedField.type === 'int') {
                                        <input [formControlName]="nestedField.fieldName" type="number" [disabled]="nestedField.noInput" />
                                      }
                                      @if (nestedField.type === 'boolean') {
                                        <input [formControlName]="nestedField.fieldName" type="checkbox" [disabled]="nestedField.noInput" />
                                      }
                                      @if (nestedField.type === 'date') {
                                        <input [formControlName]="nestedField.fieldName" type="date" [disabled]="nestedField.noInput" />
                                      }
                                      @if (nestedField.type === 'double') {
                                        <input [formControlName]="nestedField.fieldName" type="number" step="00.50" [disabled]="nestedField.noInput" />
                                      }
                                    }
                                  </div>
                                }

                                <!-- Multi-fields for nested groups -->
                                @if (nestedField.isMulti) {
                                  <div class="row-view-table-cell-multi" [formArrayName]="childGroup" [formGroupName]="n">
                                    <div [formArrayName]="nestedField.fieldName">
                                      <label>{{ nestedField.fieldName }}
                                        @if (nestedField.noInput) {<span class="no-input-indicator"> (Read Only)</span>}
                                      </label>
                                      @for (multiControl of getMultiArray(nestedField.fieldName, k, parsed.parent + '|' + childGroup, n).controls; track multiControl; let m = $index) {
                                        <div [formGroupName]="m" class="row-view-multi-item">
                                          <div class="row-view-multi-input">
                                            @if (nestedField.type === 'string') {
                                              <input [formControlName]="nestedField.fieldName" type="text"
                                                [placeholder]="(nestedField.label?.trim() || nestedField.fieldName) + ' (' + (m + 1) + ')'" [disabled]="nestedField.noInput" />
                                            }
                                            @if (nestedField.type === 'int') {
                                              <input [formControlName]="nestedField.fieldName" type="number" [disabled]="nestedField.noInput" />
                                            }
                                            @if (nestedField.type === 'boolean') {
                                              <input [formControlName]="nestedField.fieldName"
                                                type="checkbox" [disabled]="nestedField.noInput" />
                                            }
                                            @if (nestedField.type === 'date') {
                                              <input [formControlName]="nestedField.fieldName"
                                                type="date" [disabled]="nestedField.noInput" />
                                            }
                                            @if (nestedField.type === 'double') {
                                              <input [formControlName]="nestedField.fieldName"
                                                type="number" step="0.01" [disabled]="nestedField.noInput" />
                                            }
                                            @if (nestedField.foreginKey) {
                                              <div class="dropdown-input-container">
                                                <input [formControlName]="nestedField.fieldName" type="text"
                                                       class="form-input dropdown-input"
                                                       (input)="onRegularInputChange($event, nestedField.fieldName + '_nested_' + k + '_' + n + '_multi_' + m)"
                                                       (focus)="onRegularInputFocus(nestedField.fieldName + '_nested_' + k + '_' + n + '_multi_' + m)"
                                                       (blur)="onRegularInputBlur(nestedField.fieldName + '_nested_' + k + '_' + n + '_multi_' + m)"
                                                       [disabled]="nestedField.noInput"
                                                       [placeholder]="'Search ' + (nestedField.label?.trim() || nestedField.fieldName)" />

                                                <button type="button" class="dropdown-arrow-btn"
                                                        (click)="toggleRegularDropdown(nestedField.fieldName + '_nested_' + k + '_' + n + '_multi_' + m)"
                                                        [disabled]="nestedField.noInput"
                                                        matTooltip="Show options">
                                                  <mat-icon>{{ showRegularDropdown[nestedField.fieldName + '_nested_' + k + '_' + n + '_multi_' + m] ? 'keyboard_arrow_up' : 'keyboard_arrow_down' }}</mat-icon>
                                                </button>

                                                @if (showRegularDropdown[nestedField.fieldName + '_nested_' + k + '_' + n + '_multi_' + m]) {
                                                  <div class="dropdown-list">
                                                    @if (filteredRegularOptions[nestedField.fieldName + '_nested_' + k + '_' + n + '_multi_' + m] && filteredRegularOptions[nestedField.fieldName + '_nested_' + k + '_' + n + '_multi_' + m].length > 0) {
                                                      @for (option of filteredRegularOptions[nestedField.fieldName + '_nested_' + k + '_' + n + '_multi_' + m]; track option.ROW_ID) {
                                                        <div class="dropdown-item" (click)="selectRegularOption(option, nestedField.fieldName + '_nested_' + k + '_' + n + '_multi_' + m)">
                                                          @for (key of getKeys(option); track key) {
                                                            {{ option[key] }}&nbsp;
                                                          }
                                                        </div>
                                                      }
                                                    } @else {
                                                      <div class="dropdown-empty">
                                                        No options found
                                                      </div>
                                                    }
                                                  </div>
                                                }
                                              </div>
                                            }
                                          </div>
                                          <div class="row-view-multi-buttons">
                                            @if (getMultiArray(nestedField.fieldName, k, parsed.parent + '|' + childGroup, n).controls.length > 1 && !isViewMode && !nestedField.noInput) {
                                              <button mat-icon-button color="warn" type="button" (click)="removeMultiField(nestedField.fieldName, m, k, parsed.parent + '|' + childGroup, n)" matTooltip="Delete">
                                                <mat-icon>delete</mat-icon>
                                              </button>
                                            }
                                            @if (!isViewMode && !nestedField.noInput) {
                                              <button mat-icon-button color="primary" type="button" (click)="addMultiField(nestedField, k, m, parsed.parent + '|' + childGroup, n)" matTooltip="Add">
                                                <mat-icon>add</mat-icon>
                                              </button>
                                            }
                                          </div>
                                        </div>
                                      }
                                    </div>
                                  </div>
                                }
                              }

                              <!-- Nested group control buttons -->
                              <div class="row-view-table-actions" [formArrayName]="childGroup" [formGroupName]="n">
                                @if (!isViewMode) {
                                  <button mat-icon-button color="warn" type="button" (click)="removeNestedGroup(parsed.parent + '|' + childGroup, k, n)" matTooltip="Remove {{ childGroup }} Group" [disabled]="getNestedGroupArray(parsed.parent + '|' + childGroup, k).controls.length <= 1">
                                    <mat-icon>delete</mat-icon>
                                  </button>
                                }
                                @if (!isViewMode) {
                                  <button mat-icon-button color="primary" type="button" (click)="addNestedGroup(parsed.parent + '|' + childGroup, k, n)" matTooltip="Add {{ childGroup }} Group">
                                    <mat-icon>add</mat-icon>
                                  </button>
                                }
                                @if (!isViewMode) {
                                  <button mat-icon-button color="accent" type="button" (click)="cloneNestedGroup(parsed.parent + '|' + childGroup, k, n)" matTooltip="Clone {{ childGroup }} Group">
                                    <mat-icon>content_copy</mat-icon>
                                  </button>
                                }
                              </div>
                            }
                          }

                          <!-- Main group action buttons -->
                          <div class="row-view-table-actions">
                            @if (!isViewMode) {
                              <button mat-icon-button color="warn" type="button" (click)="removeGroup(parsed.parent, k)" matTooltip="Remove Group" [disabled]="getGroupArray(parsed.parent).controls.length <= 1">
                                <mat-icon>delete</mat-icon>
                              </button>
                              <button mat-icon-button color="primary" type="button" (click)="addGroup(parsed.parent, k)" matTooltip="Add Group">
                                <mat-icon>add</mat-icon>
                              </button>
                              <button mat-icon-button color="accent" type="button" (click)="cloneGroup(parsed.parent, k)" matTooltip="Clone Group">
                                <mat-icon>content_copy</mat-icon>
                              </button>
                            }
                          </div>
                        </div>
                      } @else {
                        <!-- Nested View: Original layout -->
                        <div [class]="isRowView ? 'group-fields row-view-fields' : 'group-fields'">

                        <!-- Direct fields of parent group -->
                        @for (groupField of getFieldsForGroup(parsed.parent); track groupField.fieldName) {

                        @if (!groupField.isMulti) {
                          <div class="form-field">
                            <label>{{ groupField.fieldName }} @if (groupField.mandatory) {<span>*</span>}
                              @if (groupField.noInput) {<span class="no-input-indicator"> (Read Only)</span>}
                            </label>
                            @if (groupField.foreginKey) {
                              <!-- Check if this is a type field (fieldType) -->
                              @if (groupField.foreginKey === 'fieldType') {
                                <div class="dropdown-input-container">
                                  <input [formControlName]="groupField.fieldName" type="text" 
                                         class="form-input dropdown-input" 
                                         (input)="onTypeInputChange($event, groupField.fieldName + '_group_' + k)" 
                                         (focus)="onTypeInputFocus(groupField.fieldName + '_group_' + k)" 
                                         (blur)="onTypeInputBlur(groupField.fieldName + '_group_' + k)"
                                         [disabled]="groupField.noInput"
                                         [placeholder]="'Search ' + (groupField.label?.trim() || groupField.fieldName)" />
                                  
                                  <button type="button" class="dropdown-arrow-btn" 
                                          (click)="toggleTypeDropdown(groupField.fieldName + '_group_' + k)" 
                                          [disabled]="groupField.noInput"
                                          matTooltip="Show type suggestions">
                                    <mat-icon>{{ showTypeDropdown[groupField.fieldName + '_group_' + k] ? 'keyboard_arrow_up' : 'keyboard_arrow_down' }}</mat-icon>
                                  </button>
                                  
                                  @if (showTypeDropdown[groupField.fieldName + '_group_' + k]) {
                                    <div class="dropdown-list">
                                      @if (filteredTypeOptions[groupField.fieldName + '_group_' + k] && filteredTypeOptions[groupField.fieldName + '_group_' + k].length > 0) {
                                        @for (option of filteredTypeOptions[groupField.fieldName + '_group_' + k]; track option.ROW_ID) {
                                          <div class="dropdown-item" (click)="selectTypeOption(option, groupField.fieldName + '_group_' + k)">
                                            {{ option.ROW_ID }}
                                          </div>
                                        }
                                      } @else {
                                        <div class="dropdown-empty">
                                          No types found
                                        </div>
                                      }
                                    </div>
                                  }
                                </div>
                              }
                              <!-- Check if this is a foreign key field (formDefinition) -->
                              @else if (groupField.foreginKey === 'formDefinition') {
                                <div class="dropdown-input-container">
                                  <input [formControlName]="groupField.fieldName" type="text" 
                                         class="form-input dropdown-input" 
                                         (input)="onForeignKeyInputChange($event, groupField.fieldName + '_group_' + k)" 
                                         (focus)="onForeignKeyInputFocus(groupField.fieldName + '_group_' + k)" 
                                         (blur)="onForeignKeyInputBlur(groupField.fieldName + '_group_' + k)"
                                         [disabled]="groupField.noInput"
                                         [placeholder]="'Search ' + (groupField.label?.trim() || groupField.fieldName)" />
                                  
                                  <button type="button" class="dropdown-arrow-btn" 
                                          (click)="toggleForeignKeyDropdown(groupField.fieldName + '_group_' + k)" 
                                          [disabled]="groupField.noInput"
                                          matTooltip="Show foreign key suggestions">
                                    <mat-icon>{{ showForeignKeyDropdown[groupField.fieldName + '_group_' + k] ? 'keyboard_arrow_up' : 'keyboard_arrow_down' }}</mat-icon>
                                  </button>
                                  
                                  @if (showForeignKeyDropdown[groupField.fieldName + '_group_' + k]) {
                                    <div class="dropdown-list">
                                      @if (filteredForeignKeyOptions[groupField.fieldName + '_group_' + k] && filteredForeignKeyOptions[groupField.fieldName + '_group_' + k].length > 0) {
                                        @for (option of filteredForeignKeyOptions[groupField.fieldName + '_group_' + k]; track option.ROW_ID) {
                                          <div class="dropdown-item" (click)="selectForeignKeyOption(option, groupField.fieldName + '_group_' + k)">
                                            {{ option.ROW_ID }}
                                          </div>
                                        }
                                      } @else {
                                        <div class="dropdown-empty">
                                          No foreign keys found
                                        </div>
                                      }
                                    </div>
                                  }
                                </div>
                              }
                              <!-- Default dropdown for other foreign keys -->
                              @else {
                                <div class="dropdown-input-container">
                                  <input [formControlName]="groupField.fieldName" type="text" 
                                         class="form-input dropdown-input" 
                                         (input)="onRegularInputChange($event, groupField.fieldName + '_group_' + k)" 
                                         (focus)="onRegularInputFocus(groupField.fieldName + '_group_' + k)" 
                                         (blur)="onRegularInputBlur(groupField.fieldName + '_group_' + k)"
                                         [disabled]="groupField.noInput"
                                         [placeholder]="'Search ' + (groupField.label?.trim() || groupField.fieldName)" />
                                  
                                  <button type="button" class="dropdown-arrow-btn" 
                                          (click)="toggleRegularDropdown(groupField.fieldName + '_group_' + k)" 
                                          [disabled]="groupField.noInput"
                                          matTooltip="Show options">
                                    <mat-icon>{{ showRegularDropdown[groupField.fieldName + '_group_' + k] ? 'keyboard_arrow_up' : 'keyboard_arrow_down' }}</mat-icon>
                                  </button>
                                  
                                  @if (showRegularDropdown[groupField.fieldName + '_group_' + k]) {
                                    <div class="dropdown-list">
                                      @if (filteredRegularOptions[groupField.fieldName + '_group_' + k] && filteredRegularOptions[groupField.fieldName + '_group_' + k].length > 0) {
                                        @for (option of filteredRegularOptions[groupField.fieldName + '_group_' + k]; track option.ROW_ID) {
                                          <div class="dropdown-item" (click)="selectRegularOption(option, groupField.fieldName + '_group_' + k)">
                                            @for (key of getKeys(option); track key) {
                                              {{ option[key] }}&nbsp;
                                            }
                                          </div>
                                        }
                                      } @else {
                                        <div class="dropdown-empty">
                                          No options found
                                        </div>
                                      }
                                    </div>
                                  }
                                </div>
                              }
                            } @else {
                              <!-- Regular input fields for non-foreign key grouped fields -->
                              @if (groupField.type === 'string') {
                                <input [formControlName]="groupField.fieldName" type="text"
                                  [placeholder]="(groupField.label?.trim() || groupField.fieldName)" [disabled]="groupField.noInput" />
                              }
                              @if (groupField.type === 'int') {
                                <input [formControlName]="groupField.fieldName" type="number" [disabled]="groupField.noInput" />
                              }
                              @if (groupField.type === 'boolean') {
                                <input [formControlName]="groupField.fieldName"
                                  type="checkbox" [disabled]="groupField.noInput" />
                              }
                              @if (groupField.type === 'date') {
                                <input [formControlName]="groupField.fieldName" type="date" [disabled]="groupField.noInput" />
                              }
                              @if (groupField.type === 'double') {
                                <input [formControlName]="groupField.fieldName" type="number"
                                  step="00.50" [disabled]="groupField.noInput" />
                              }
                            }
                          </div>
                        }

                        @if (groupField.isMulti) {
                          <div [formArrayName]="groupField.fieldName">
                            <h4>{{ groupField.fieldName }}
                              @if (groupField.noInput) {<span class="no-input-indicator"> (Read Only)</span>}
                            </h4>

                            @for (multiControl of getMultiArray(groupField.fieldName, k, field.Group).controls; track multiControl; let l = $index) {
                              <div [formGroupName]="l" class="form-field">
                                <label>{{ groupField.fieldName }} ({{ l + 1 }})</label>

                                @if (groupField.type === 'string') {
                                  <input [formControlName]="groupField.fieldName" type="text"
                                    [placeholder]="(groupField.label?.trim() || groupField.fieldName) + ' (' + (l + 1) + ')'" [disabled]="groupField.noInput" />
                                }

                                @if (groupField.type === 'int') {
                                  <input [formControlName]="groupField.fieldName"
                                    type="number" [disabled]="groupField.noInput" />
                                }

                                @if (groupField.type === 'boolean') {
                                  <input [formControlName]="groupField.fieldName"
                                    type="checkbox" [disabled]="groupField.noInput" />
                                }

                                @if (groupField.type === 'date') {
                                  <input [formControlName]="groupField.fieldName"
                                    type="date" [disabled]="groupField.noInput" />
                                }

                                @if (groupField.type === 'double') {
                                  <input [formControlName]="groupField.fieldName"
                                    type="number" step="0.01" [disabled]="groupField.noInput" />
                                }

                                @if (groupField.foreginKey) {
                                  <!-- Check if this is a type field (fieldType) -->
                                  @if (groupField.foreginKey === 'fieldType') {
                                    <div class="dropdown-input-container">
                                      <input [formControlName]="groupField.fieldName" type="text" 
                                             class="form-input dropdown-input" 
                                             (input)="onTypeInputChange($event, groupField.fieldName + '_group_' + k + '_multi_' + l)" 
                                             (focus)="onTypeInputFocus(groupField.fieldName + '_group_' + k + '_multi_' + l)" 
                                             (blur)="onTypeInputBlur(groupField.fieldName + '_group_' + k + '_multi_' + l)"
                                             [disabled]="groupField.noInput"
                                             [placeholder]="'Search ' + (groupField.label?.trim() || groupField.fieldName)" />
                                      
                                      <button type="button" class="dropdown-arrow-btn" 
                                              (click)="toggleTypeDropdown(groupField.fieldName + '_group_' + k + '_multi_' + l)" 
                                              [disabled]="groupField.noInput"
                                              matTooltip="Show type suggestions">
                                        <mat-icon>{{ showTypeDropdown[groupField.fieldName + '_group_' + k + '_multi_' + l] ? 'keyboard_arrow_up' : 'keyboard_arrow_down' }}</mat-icon>
                                      </button>
                                      
                                      @if (showTypeDropdown[groupField.fieldName + '_group_' + k + '_multi_' + l]) {
                                        <div class="dropdown-list">
                                          @if (filteredTypeOptions[groupField.fieldName + '_group_' + k + '_multi_' + l] && filteredTypeOptions[groupField.fieldName + '_group_' + k + '_multi_' + l].length > 0) {
                                            @for (option of filteredTypeOptions[groupField.fieldName + '_group_' + k + '_multi_' + l]; track option.ROW_ID) {
                                              <div class="dropdown-item" (click)="selectTypeOption(option, groupField.fieldName + '_group_' + k + '_multi_' + l)">
                                                {{ option.ROW_ID }}
                                              </div>
                                            }
                                          } @else {
                                            <div class="dropdown-empty">
                                              No types found
                                            </div>
                                          }
                                        </div>
                                      }
                                    </div>
                                  }
                                  <!-- Check if this is a foreign key field (formDefinition) -->
                                  @else if (groupField.foreginKey === 'formDefinition') {
                                    <div class="dropdown-input-container">
                                      <input [formControlName]="groupField.fieldName" type="text" 
                                             class="form-input dropdown-input" 
                                             (input)="onForeignKeyInputChange($event, groupField.fieldName + '_group_' + k + '_multi_' + l)" 
                                             (focus)="onForeignKeyInputFocus(groupField.fieldName + '_group_' + k + '_multi_' + l)" 
                                             (blur)="onForeignKeyInputBlur(groupField.fieldName + '_group_' + k + '_multi_' + l)"
                                             [disabled]="groupField.noInput"
                                             [placeholder]="'Search ' + (groupField.label?.trim() || groupField.fieldName)" />
                                      
                                      <button type="button" class="dropdown-arrow-btn" 
                                              (click)="toggleForeignKeyDropdown(groupField.fieldName + '_group_' + k + '_multi_' + l)" 
                                              [disabled]="groupField.noInput"
                                              matTooltip="Show foreign key suggestions">
                                        <mat-icon>{{ showForeignKeyDropdown[groupField.fieldName + '_group_' + k + '_multi_' + l] ? 'keyboard_arrow_up' : 'keyboard_arrow_down' }}</mat-icon>
                                      </button>
                                      
                                      @if (showForeignKeyDropdown[groupField.fieldName + '_group_' + k + '_multi_' + l]) {
                                        <div class="dropdown-list">
                                          @if (filteredForeignKeyOptions[groupField.fieldName + '_group_' + k + '_multi_' + l] && filteredForeignKeyOptions[groupField.fieldName + '_group_' + k + '_multi_' + l].length > 0) {
                                            @for (option of filteredForeignKeyOptions[groupField.fieldName + '_group_' + k + '_multi_' + l]; track option.ROW_ID) {
                                              <div class="dropdown-item" (click)="selectForeignKeyOption(option, groupField.fieldName + '_group_' + k + '_multi_' + l)">
                                                {{ option.ROW_ID }}
                                              </div>
                                            }
                                          } @else {
                                            <div class="dropdown-empty">
                                              No foreign keys found
                                            </div>
                                          }
                                        </div>
                                      }
                                    </div>
                                  }
                                  <!-- Default dropdown for other foreign keys -->
                                  @else {
                                    <div class="dropdown-input-container">
                                      <input [formControlName]="groupField.fieldName" type="text" 
                                             class="form-input dropdown-input" 
                                             (input)="onRegularInputChange($event, groupField.fieldName + '_group_' + k + '_multi_' + l)" 
                                             (focus)="onRegularInputFocus(groupField.fieldName + '_group_' + k + '_multi_' + l)" 
                                             (blur)="onRegularInputBlur(groupField.fieldName + '_group_' + k + '_multi_' + l)"
                                             [disabled]="groupField.noInput"
                                             [placeholder]="'Search ' + (groupField.label?.trim() || groupField.fieldName)" />
                                      
                                      <button type="button" class="dropdown-arrow-btn" 
                                              (click)="toggleRegularDropdown(groupField.fieldName + '_group_' + k + '_multi_' + l)" 
                                              [disabled]="groupField.noInput"
                                              matTooltip="Show options">
                                        <mat-icon>{{ showRegularDropdown[groupField.fieldName + '_group_' + k + '_multi_' + l] ? 'keyboard_arrow_up' : 'keyboard_arrow_down' }}</mat-icon>
                                      </button>
                                      
                                      @if (showRegularDropdown[groupField.fieldName + '_group_' + k + '_multi_' + l]) {
                                        <div class="dropdown-list">
                                          @if (filteredRegularOptions[groupField.fieldName + '_group_' + k + '_multi_' + l] && filteredRegularOptions[groupField.fieldName + '_group_' + k + '_multi_' + l].length > 0) {
                                            @for (option of filteredRegularOptions[groupField.fieldName + '_group_' + k + '_multi_' + l]; track option.ROW_ID) {
                                              <div class="dropdown-item" (click)="selectRegularOption(option, groupField.fieldName + '_group_' + k + '_multi_' + l)">
                                                @for (key of getKeys(option); track key) {
                                                  {{ option[key] }}&nbsp;
                                                }
                                              </div>
                                            }
                                          } @else {
                                            <div class="dropdown-empty">
                                              No options found
                                            </div>
                                          }
                                        </div>
                                      }
                                    </div>
                                  }
                                }

                                @if (getMultiArray(groupField.fieldName, k, field.Group).controls.length > 1 && !isViewMode && !groupField.noInput) {
                                  <button mat-icon-button color="warn" type="button" (click)="removeMultiField(groupField.fieldName, l, k, field.Group)" matTooltip="Delete">
                                    <mat-icon>delete</mat-icon>
                                  </button>
                                }

                                @if (!isViewMode && !groupField.noInput) {
                                  <button mat-icon-button color="primary" type="button" (click)="addMultiField(groupField, k, l, field.Group)" matTooltip="Add">
                                    <mat-icon>add</mat-icon>
                                  </button>
                                }
                              </div>
                            }
                          </div>
                        }
                      }

                      <!-- Nested subgroups -->
                      @for (childGroup of getChildGroups(parsed.parent); track childGroup) {
                        <div [formArrayName]="childGroup" [class]="isRowView ? 'nested-group-section row-view-nested-seamless' : 'nested-group-section'">
                          <h4 [class.row-view-hidden]="isRowView">{{ childGroup }}</h4>
                          @for (nestedGroup of getNestedGroupArray(parsed.parent + '|' + childGroup, k).controls; track nestedGroup; let n = $index) {
                            <div [formGroupName]="n" [class]="isRowView ? 'form-grid nested-field row-view-nested-field-seamless' : 'form-grid nested-field'">
                              <div [class]="isRowView ? 'nested-group-fields row-view-nested-fields-seamless' : 'nested-group-fields'">
                                @for (nestedField of getFieldsForGroupPath(parsed.parent + '|' + childGroup); track nestedField.fieldName) {

                                  @if (!nestedField.isMulti) {
                                    <div class="form-field">
                                      <label>{{ nestedField.fieldName }} @if (nestedField.mandatory) {<span>*</span>}
                                        @if (nestedField.noInput) {<span class="no-input-indicator"> (Read Only)</span>}
                                      </label>
                                      @if (nestedField.type === 'string') {
                                        <input [formControlName]="nestedField.fieldName" type="text"
                                          [placeholder]="(nestedField.label.trim() || nestedField.fieldName)" [disabled]="nestedField.noInput" />
                                      }
                                      @if (nestedField.type === 'int') {
                                        <input [formControlName]="nestedField.fieldName" type="number" [disabled]="nestedField.noInput" />
                                      }
                                      @if (nestedField.type === 'boolean') {
                                        <input [formControlName]="nestedField.fieldName"
                                          type="checkbox" [disabled]="nestedField.noInput" />
                                      }
                                      @if (nestedField.type === 'date') {
                                        <input [formControlName]="nestedField.fieldName" type="date" [disabled]="nestedField.noInput" />
                                      }
                                      @if (nestedField.type === 'double') {
                                        <input [formControlName]="nestedField.fieldName" type="number"
                                          step="00.50" [disabled]="nestedField.noInput" />
                                      }

                                      @if (nestedField.foreginKey) {
                                        <!-- Check if this is a type field (fieldType) -->
                                        @if (nestedField.foreginKey === 'fieldType') {
                                          <div class="dropdown-input-container">
                                            <input [formControlName]="nestedField.fieldName" type="text"
                                                   class="form-input dropdown-input"
                                                   (input)="onTypeInputChange($event, nestedField.fieldName + '_nested_' + k + '_' + n)"
                                                   (focus)="onTypeInputFocus(nestedField.fieldName + '_nested_' + k + '_' + n)"
                                                   (blur)="onTypeInputBlur(nestedField.fieldName + '_nested_' + k + '_' + n)"
                                                   [disabled]="nestedField.noInput"
                                                   [placeholder]="'Search ' + (nestedField.label.trim() || nestedField.fieldName)" />

                                            <button type="button" class="dropdown-arrow-btn"
                                                    (click)="toggleTypeDropdown(nestedField.fieldName + '_nested_' + k + '_' + n)"
                                                    [disabled]="nestedField.noInput"
                                                    matTooltip="Show type suggestions">
                                              <mat-icon>{{ showTypeDropdown[nestedField.fieldName + '_nested_' + k + '_' + n] ? 'keyboard_arrow_up' : 'keyboard_arrow_down' }}</mat-icon>
                                            </button>

                                            @if (showTypeDropdown[nestedField.fieldName + '_nested_' + k + '_' + n]) {
                                              <div class="dropdown-list">
                                                @if (filteredTypeOptions[nestedField.fieldName + '_nested_' + k + '_' + n] && filteredTypeOptions[nestedField.fieldName + '_nested_' + k + '_' + n].length > 0) {
                                                  @for (option of filteredTypeOptions[nestedField.fieldName + '_nested_' + k + '_' + n]; track option.ROW_ID) {
                                                    <div class="dropdown-item" (click)="selectTypeOption(option, nestedField.fieldName + '_nested_' + k + '_' + n)">
                                                      {{ option.ROW_ID }}
                                                    </div>
                                                  }
                                                } @else {
                                                  <div class="dropdown-empty">
                                                    No types found
                                                  </div>
                                                }
                                              </div>
                                            }
                                          </div>
                                        }
                                        <!-- Check if this is a foreign key field (formDefinition) -->
                                        @else if (nestedField.foreginKey === 'formDefinition') {
                                          <div class="dropdown-input-container">
                                            <input [formControlName]="nestedField.fieldName" type="text"
                                                   class="form-input dropdown-input"
                                                   (input)="onForeignKeyInputChange($event, nestedField.fieldName + '_nested_' + k + '_' + n)"
                                                   (focus)="onForeignKeyInputFocus(nestedField.fieldName + '_nested_' + k + '_' + n)"
                                                   (blur)="onForeignKeyInputBlur(nestedField.fieldName + '_nested_' + k + '_' + n)"
                                                   [disabled]="nestedField.noInput"
                                                   [placeholder]="'Search ' + (nestedField.label.trim() || nestedField.fieldName)" />

                                            <button type="button" class="dropdown-arrow-btn"
                                                    (click)="toggleForeignKeyDropdown(nestedField.fieldName + '_nested_' + k + '_' + n)"
                                                    [disabled]="nestedField.noInput"
                                                    matTooltip="Show foreign key suggestions">
                                              <mat-icon>{{ showForeignKeyDropdown[nestedField.fieldName + '_nested_' + k + '_' + n] ? 'keyboard_arrow_up' : 'keyboard_arrow_down' }}</mat-icon>
                                            </button>

                                            @if (showForeignKeyDropdown[nestedField.fieldName + '_nested_' + k + '_' + n]) {
                                              <div class="dropdown-list">
                                                @if (filteredForeignKeyOptions[nestedField.fieldName + '_nested_' + k + '_' + n] && filteredForeignKeyOptions[nestedField.fieldName + '_nested_' + k + '_' + n].length > 0) {
                                                  @for (option of filteredForeignKeyOptions[nestedField.fieldName + '_nested_' + k + '_' + n]; track option.ROW_ID) {
                                                    <div class="dropdown-item" (click)="selectForeignKeyOption(option, nestedField.fieldName + '_nested_' + k + '_' + n)">
                                                      {{ option.ROW_ID }}
                                                    </div>
                                                  }
                                                } @else {
                                                  <div class="dropdown-empty">
                                                    No foreign keys found
                                                  </div>
                                                }
                                              </div>
                                            }
                                          </div>
                                        }
                                        <!-- Default dropdown for other foreign keys -->
                                        @else {
                                          <div class="dropdown-input-container">
                                            <input [formControlName]="nestedField.fieldName" type="text"
                                                   class="form-input dropdown-input"
                                                   (input)="onRegularInputChange($event, nestedField.fieldName + '_nested_' + k + '_' + n)"
                                                   (focus)="onRegularInputFocus(nestedField.fieldName + '_nested_' + k + '_' + n)"
                                                   (blur)="onRegularInputBlur(nestedField.fieldName + '_nested_' + k + '_' + n)"
                                                   [disabled]="nestedField.noInput"
                                                   [placeholder]="'Search ' + (nestedField.label.trim() || nestedField.fieldName)" />

                                            <button type="button" class="dropdown-arrow-btn"
                                                    (click)="toggleRegularDropdown(nestedField.fieldName + '_nested_' + k + '_' + n)"
                                                    [disabled]="nestedField.noInput"
                                                    matTooltip="Show options">
                                              <mat-icon>{{ showRegularDropdown[nestedField.fieldName + '_nested_' + k + '_' + n] ? 'keyboard_arrow_up' : 'keyboard_arrow_down' }}</mat-icon>
                                            </button>

                                            @if (showRegularDropdown[nestedField.fieldName + '_nested_' + k + '_' + n]) {
                                              <div class="dropdown-list">
                                                @if (filteredRegularOptions[nestedField.fieldName + '_nested_' + k + '_' + n] && filteredRegularOptions[nestedField.fieldName + '_nested_' + k + '_' + n].length > 0) {
                                                  @for (option of filteredRegularOptions[nestedField.fieldName + '_nested_' + k + '_' + n]; track option.ROW_ID) {
                                                    <div class="dropdown-item" (click)="selectRegularOption(option, nestedField.fieldName + '_nested_' + k + '_' + n)">
                                                      @for (key of getKeys(option); track key) {
                                                        {{ option[key] }}&nbsp;
                                                      }
                                                    </div>
                                                  }
                                                } @else {
                                                  <div class="dropdown-empty">
                                                    No options found
                                                  </div>
                                                }
                                              </div>
                                            }
                                          </div>
                                        }
                                      }
                                    </div>
                                  }

                                  @if (nestedField.isMulti) {
                                    <div [formArrayName]="nestedField.fieldName">
                                      <h5>{{ nestedField.fieldName }}
                                        @if (nestedField.noInput) {<span class="no-input-indicator"> (Read Only)</span>}
                                      </h5>

                                      @for (multiControl of getMultiArray(nestedField.fieldName, k, parsed.parent + '|' + childGroup, n).controls; track multiControl; let m = $index) {
                                        <div [formGroupName]="m" class="form-field is-multi">
                                          <label>{{ nestedField.fieldName }} ({{ m + 1 }})</label>

                                          <div class="multi-input-container">
                                            <div class="multi-input">
                                              @if (nestedField.type === 'string') {
                                                <input [formControlName]="nestedField.fieldName" type="text"
                                                  [placeholder]="(nestedField.label.trim() || nestedField.fieldName) + ' (' + (m + 1) + ')'" [disabled]="nestedField.noInput" />
                                              }

                                              @if (nestedField.type === 'int') {
                                                <input [formControlName]="nestedField.fieldName"
                                                  type="number" [disabled]="nestedField.noInput" />
                                              }

                                              @if (nestedField.type === 'boolean') {
                                                <input [formControlName]="nestedField.fieldName"
                                                  type="checkbox" [disabled]="nestedField.noInput" />
                                              }

                                              @if (nestedField.type === 'date') {
                                                <input [formControlName]="nestedField.fieldName"
                                                  type="date" [disabled]="nestedField.noInput" />
                                              }

                                              @if (nestedField.type === 'double') {
                                                <input [formControlName]="nestedField.fieldName"
                                                  type="number" step="0.01" [disabled]="nestedField.noInput" />
                                              }

                                              @if (nestedField.foreginKey) {
                                                <!-- Similar dropdown logic for multi-fields in nested groups -->
                                                <div class="dropdown-input-container">
                                                  <input [formControlName]="nestedField.fieldName" type="text"
                                                         class="form-input dropdown-input"
                                                         (input)="onRegularInputChange($event, nestedField.fieldName + '_nested_' + k + '_' + n + '_multi_' + m)"
                                                         (focus)="onRegularInputFocus(nestedField.fieldName + '_nested_' + k + '_' + n + '_multi_' + m)"
                                                         (blur)="onRegularInputBlur(nestedField.fieldName + '_nested_' + k + '_' + n + '_multi_' + m)"
                                                         [disabled]="nestedField.noInput"
                                                         [placeholder]="'Search ' + (nestedField.label.trim() || nestedField.fieldName)" />

                                                  <button type="button" class="dropdown-arrow-btn"
                                                          (click)="toggleRegularDropdown(nestedField.fieldName + '_nested_' + k + '_' + n + '_multi_' + m)"
                                                          [disabled]="nestedField.noInput"
                                                          matTooltip="Show options">
                                                    <mat-icon>{{ showRegularDropdown[nestedField.fieldName + '_nested_' + k + '_' + n + '_multi_' + m] ? 'keyboard_arrow_up' : 'keyboard_arrow_down' }}</mat-icon>
                                                  </button>

                                                  @if (showRegularDropdown[nestedField.fieldName + '_nested_' + k + '_' + n + '_multi_' + m]) {
                                                    <div class="dropdown-list">
                                                      @if (filteredRegularOptions[nestedField.fieldName + '_nested_' + k + '_' + n + '_multi_' + m] && filteredRegularOptions[nestedField.fieldName + '_nested_' + k + '_' + n + '_multi_' + m].length > 0) {
                                                        @for (option of filteredRegularOptions[nestedField.fieldName + '_nested_' + k + '_' + n + '_multi_' + m]; track option.ROW_ID) {
                                                          <div class="dropdown-item" (click)="selectRegularOption(option, nestedField.fieldName + '_nested_' + k + '_' + n + '_multi_' + m)">
                                                            @for (key of getKeys(option); track key) {
                                                              {{ option[key] }}&nbsp;
                                                            }
                                                          </div>
                                                        }
                                                      } @else {
                                                        <div class="dropdown-empty">
                                                          No options found
                                                        </div>
                                                      }
                                                    </div>
                                                  }
                                                </div>
                                              }
                                            </div>

                                            <div class="multi-buttons">
                                              @if (getMultiArray(nestedField.fieldName, k, parsed.parent + '|' + childGroup, n).controls.length > 1 && !isViewMode && !nestedField.noInput) {
                                                <button mat-icon-button color="warn" type="button" (click)="removeMultiField(nestedField.fieldName, m, k, parsed.parent + '|' + childGroup, n)" matTooltip="Delete">
                                                  <mat-icon>delete</mat-icon>
                                                </button>
                                              }

                                              @if (!isViewMode && !nestedField.noInput) {
                                                <button mat-icon-button color="primary" type="button" (click)="addMultiField(nestedField, k, m, parsed.parent + '|' + childGroup, n)" matTooltip="Add">
                                                  <mat-icon>add</mat-icon>
                                                </button>
                                              }
                                            </div>
                                          </div>
                                        </div>
                                      }
                                    </div>
                                  }
                                }

                                <!-- Nested group control buttons -->
                                <div class="nested-group-buttons">
                                  @if (!isViewMode) {
                                    <button mat-icon-button color="warn" type="button" (click)="removeNestedGroup(parsed.parent + '|' + childGroup, k, n)" matTooltip="Remove {{ childGroup }} Group" [disabled]="getNestedGroupArray(parsed.parent + '|' + childGroup, k).controls.length <= 1">
                                      <mat-icon>delete</mat-icon>
                                    </button>
                                  }
                                  @if (!isViewMode) {
                                    <button mat-icon-button color="primary" type="button" (click)="addNestedGroup(parsed.parent + '|' + childGroup, k, n)" matTooltip="Add {{ childGroup }} Group">
                                      <mat-icon>add</mat-icon>
                                    </button>
                                  }
                                  @if (!isViewMode) {
                                    <button mat-icon-button color="accent" type="button" (click)="cloneNestedGroup(parsed.parent + '|' + childGroup, k, n)" matTooltip="Clone {{ childGroup }} Group">
                                      <mat-icon>content_copy</mat-icon>
                                    </button>
                                  }
                                </div>
                              </div>
                            </div>
                          }
                        </div>
                      }

                        <!-- Group control buttons for nested view -->
                        <div class="group-buttons">
                          @if (!isViewMode) {
                            <button mat-icon-button color="warn" type="button" (click)="removeGroup(parsed.parent, k)" matTooltip="Remove Group" [disabled]="getGroupArray(parsed.parent).controls.length <= 1">
                              <mat-icon>delete</mat-icon>
                            </button>
                          }
                          @if (!isViewMode) {
                            <button mat-icon-button color="primary" type="button" (click)="addGroup(parsed.parent, k)" matTooltip="Add Group">
                              <mat-icon>add</mat-icon>
                            </button>
                          }
                          @if (!isViewMode) {
                            <button mat-icon-button color="accent" type="button" (click)="cloneGroup(parsed.parent, k)" matTooltip="Clone Group">
                              <mat-icon>content_copy</mat-icon>
                            </button>
                          }
                        </div>
                      </div>
                      }

                    </div>
                  }
                </div>
              }
            }
          }
      
         </ng-container>
    </div>
  </div>
      </form>
    </div>
  }
} @else {
  <div class="success-message">Record submitted successfully!</div>
}
