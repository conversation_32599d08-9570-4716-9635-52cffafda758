/* Multi-Field Component Styling - Extracted from main component */

/* Multi-field container */
.multi-field-container {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

/* Individual multi-field item */
.form-field.is-multi {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  flex-wrap: wrap;
  background-color: white;
  margin-bottom: 16px;
  padding: 16px;
  border-radius: 8px;
  border: 1px solid #e0e0e0;
  position: relative;
}

/* Multi-field input container - matches .is-multi .multi-input-container */
.multi-input-container {
  display: flex;
  align-items: center;
  gap: 8px;
  width: 100%;
}

/* Multi-field input - matches .is-multi .multi-input */
.multi-input {
  flex: 1;
  min-width: 0; /* Allow flex item to shrink */
}

.multi-input label {
  display: block;
  margin-bottom: 4px;
  font-weight: 500;
  color: #333;
  font-size: 14px;
}

.multi-input input,
.multi-input select {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  font-size: 14px;
  background-color: white;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.multi-input input:focus,
.multi-input select:focus {
  border-color: #9e9e9e;
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(158, 158, 158, 0.25);
}

/* Multi-field buttons - matches .is-multi .multi-buttons */
.multi-buttons {
  display: flex;
  flex-direction: row;
  gap: 4px;
  flex-shrink: 0;
  align-items: center;
}

.multi-buttons .mat-mdc-icon-button {
  width: 36px !important;
  height: 36px !important;
  padding: 0 !important;
  line-height: 1 !important;
}

.multi-buttons .mat-icon {
  font-size: 18px !important;
  width: 18px !important;
  height: 18px !important;
  line-height: 1 !important;
}

/* Row view multi-field styling - matches .row-view-table-multi-* */
.row-view-multi-container {
  display: flex;
  align-items: center;
  gap: 8px;
  width: 100%;
}

.row-view-multi-input {
  flex: 1;
  min-width: 0;
}

.row-view-multi-input input,
.row-view-multi-input select {
  width: 100%;
  padding: 4px 6px;
  font-size: 12px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  background-color: white;
}

.row-view-multi-input input:focus,
.row-view-multi-input select:focus {
  border-color: #9e9e9e;
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(158, 158, 158, 0.25);
}

.row-view-multi-buttons {
  display: flex;
  gap: 2px;
  flex-shrink: 0;
}

.row-view-multi-buttons .mat-mdc-icon-button {
  width: 24px !important;
  height: 24px !important;
  padding: 0 !important;
}

.row-view-multi-buttons .mat-icon {
  font-size: 16px !important;
  width: 16px !important;
  height: 16px !important;
}

/* Read-only styling - unified background for disabled fields */
.multi-input input[disabled],
.multi-input input[readonly],
.multi-input select[disabled] {
  background-color: #f1f3f4 !important;
  color: #5f6368 !important;
  border-color: #dadce0 !important;
  cursor: not-allowed !important;
  opacity: 1 !important;
}

.row-view-multi-input input[disabled],
.row-view-multi-input input[readonly],
.row-view-multi-input select[disabled] {
  background-color: #f1f3f4 !important;
  color: #5f6368 !important;
  border-color: #dadce0 !important;
  cursor: not-allowed !important;
  opacity: 1 !important;
}

/* No-input indicator styling */
.no-input-indicator {
  color: red !important;
  font-weight: bold !important;
  font-size: 12px;
}

/* Nested group multi-field styling - matches .nested-group-section patterns */
.nested-group-section .form-field.is-multi {
  margin-bottom: 16px;
}

.nested-group-section .multi-input-container {
  display: flex;
  align-items: center;
  gap: 8px;
  width: 100%;
}

.nested-group-section .multi-input {
  flex: 1;
  min-width: 0;
}

.nested-group-section .multi-buttons {
  display: flex;
  gap: 4px;
  flex-shrink: 0;
}

.nested-group-section .multi-input input,
.nested-group-section .multi-input select {
  width: 100%;
}

/* Visual distinction for nested multi-fields */
.nested-group-section .form-field.is-multi label {
  color: #495057;
  font-weight: 600;
  font-size: 13px;
}

.nested-group-section .form-field.is-multi .multi-input-container {
  background-color: white;
  border: 1px solid #f1f3f4;
  border-radius: 4px;
  padding: 8px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .form-field.is-multi {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
    padding: 12px;
  }

  .multi-input-container {
    flex-direction: column;
    align-items: stretch;
    gap: 8px;
  }

  .multi-buttons {
    justify-content: flex-end;
  }

  .row-view-multi-buttons .mat-mdc-icon-button {
    width: 20px !important;
    height: 20px !important;
  }

  .row-view-multi-buttons .mat-icon {
    font-size: 14px !important;
    width: 14px !important;
    height: 14px !important;
  }
}

/* Responsive multi-field styling for nested groups */
@media (max-width: 767px) {
  .nested-group-section .multi-input-container {
    flex-direction: column;
    align-items: stretch;
    gap: 6px;
  }

  .nested-group-section .multi-buttons {
    justify-content: center;
    gap: 6px;
  }
}

@media (max-width: 480px) {
  .form-field.is-multi {
    padding: 8px;
    gap: 8px;
  }

  .multi-input-container {
    gap: 6px;
  }

  .multi-buttons {
    gap: 2px;
  }

  .nested-group-section .multi-input-container {
    gap: 4px;
  }

  .nested-group-section .multi-buttons {
    gap: 4px;
  }
}
