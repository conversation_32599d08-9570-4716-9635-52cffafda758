import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ReactiveFormsModule, FormBuilder, FormGroup } from '@angular/forms';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatTooltipModule } from '@angular/material/tooltip';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';

import { MultiFieldComponent } from './multi-field.component';
import { RegularFieldComponent } from '../regular-field/regular-field.component';

describe('MultiFieldComponent', () => {
  let component: MultiFieldComponent;
  let fixture: ComponentFixture<MultiFieldComponent>;
  let formBuilder: FormBuilder;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [
        MultiFieldComponent,
        ReactiveFormsModule,
        MatIconModule,
        MatButtonModule,
        MatTooltipModule,
        NoopAnimationsModule,
        RegularFieldComponent
      ]
    })
    .compileComponents();

    fixture = TestBed.createComponent(MultiFieldComponent);
    component = fixture.componentInstance;
    formBuilder = TestBed.inject(FormBuilder);

    // Setup basic test data for Phase 1
    component.field = {
      fieldName: 'testField',
      type: 'string',
      isMulti: true,
      mandatory: false,
      noInput: false
    };

    component.form = formBuilder.group({
      testField: formBuilder.array([
        formBuilder.group({
          testField: ['test value']
        })
      ])
    });

    component.fields = [];
    component.isViewMode = false;

    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should have required inputs', () => {
    expect(component.field).toBeDefined();
    expect(component.form).toBeDefined();
    expect(component.fields).toBeDefined();
    expect(component.isViewMode).toBeDefined();
  });

  it('should have placeholder methods', () => {
    expect(typeof component.getMultiArray).toBe('function');
    expect(typeof component.addMultiField).toBe('function');
    expect(typeof component.removeMultiField).toBe('function');
    expect(typeof component.createMultiField).toBe('function');
  });

  it('should emit field value changes', () => {
    spyOn(component.fieldValueChange, 'emit');
    const testEvent = { field: 'test', value: 'test value' };
    
    component.onFieldValueChange(testEvent);
    
    expect(component.fieldValueChange.emit).toHaveBeenCalledWith(testEvent);
  });
});
