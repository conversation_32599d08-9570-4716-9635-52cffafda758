import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ReactiveFormsModule, FormBuilder, FormGroup } from '@angular/forms';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatTooltipModule } from '@angular/material/tooltip';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';

import { MultiFieldComponent } from './multi-field.component';
import { RegularFieldComponent } from '../regular-field/regular-field.component';

describe('MultiFieldComponent', () => {
  let component: MultiFieldComponent;
  let fixture: ComponentFixture<MultiFieldComponent>;
  let formBuilder: FormBuilder;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [
        MultiFieldComponent,
        ReactiveFormsModule,
        MatIconModule,
        MatButtonModule,
        MatTooltipModule,
        NoopAnimationsModule,
        RegularFieldComponent
      ]
    })
    .compileComponents();

    fixture = TestBed.createComponent(MultiFieldComponent);
    component = fixture.componentInstance;
    formBuilder = TestBed.inject(FormBuilder);

    // Setup basic test data for Phase 1
    component.field = {
      fieldName: 'testField',
      type: 'string',
      isMulti: true,
      mandatory: false,
      noInput: false
    };

    component.form = formBuilder.group({
      testField: formBuilder.array([
        formBuilder.group({
          testField: ['test value']
        })
      ])
    });

    component.fields = [];
    component.isViewMode = false;

    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should have required inputs', () => {
    expect(component.field).toBeDefined();
    expect(component.form).toBeDefined();
    expect(component.fields).toBeDefined();
    expect(component.isViewMode).toBeDefined();
  });

  it('should have multi-field methods', () => {
    expect(typeof component.getMultiArray).toBe('function');
    expect(typeof component.addMultiField).toBe('function');
    expect(typeof component.removeMultiField).toBe('function');
    expect(typeof component.createMultiField).toBe('function');
    expect(typeof component.parseGroupPath).toBe('function');
    expect(typeof component.getGroupArray).toBe('function');
  });

  it('should get multi array correctly', () => {
    const multiArray = component.getMultiArray('testField');
    expect(multiArray).toBeDefined();
    expect(multiArray.length).toBe(1);
  });

  it('should create multi field correctly', () => {
    const newField = component.createMultiField(component.field);
    expect(newField).toBeDefined();
    expect(newField.get('testField')).toBeDefined();
  });

  it('should parse group path correctly', () => {
    const parsed = component.parseGroupPath('parentGroup|childGroup');
    expect(parsed.isNested).toBe(true);
    expect(parsed.parent).toBe('parentGroup');
    expect(parsed.child).toBe('childGroup');
  });

  it('should handle non-nested group path', () => {
    const parsed = component.parseGroupPath('simpleGroup');
    expect(parsed.isNested).toBe(false);
    expect(parsed.parent).toBe('simpleGroup');
    expect(parsed.child).toBe(null);
  });

  it('should add multi field', () => {
    const initialLength = component.getMultiArray('testField').length;
    component.addMultiField(component.field);
    expect(component.getMultiArray('testField').length).toBe(initialLength + 1);
  });

  it('should remove multi field', () => {
    // Add an extra field first
    component.addMultiField(component.field);
    const initialLength = component.getMultiArray('testField').length;

    component.removeMultiField('testField', 0);
    expect(component.getMultiArray('testField').length).toBe(initialLength - 1);
  });

  it('should handle disabled fields in createMultiField', () => {
    const disabledField = {
      ...component.field,
      noInput: true
    };

    const newField = component.createMultiField(disabledField);
    const fieldControl = newField.get('testField');
    expect(fieldControl?.disabled).toBe(true);
  });

  it('should emit field value changes', () => {
    spyOn(component.fieldValueChange, 'emit');
    const testEvent = { field: 'test', value: 'test value' };
    
    component.onFieldValueChange(testEvent);
    
    expect(component.fieldValueChange.emit).toHaveBeenCalledWith(testEvent);
  });
});
