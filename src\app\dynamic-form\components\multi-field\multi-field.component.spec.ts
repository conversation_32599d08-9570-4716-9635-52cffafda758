import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ReactiveFormsModule, FormBuilder, FormArray, FormGroup } from '@angular/forms';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatTooltipModule } from '@angular/material/tooltip';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';

import { MultiFieldComponent } from './multi-field.component';
import { RegularFieldComponent } from '../regular-field/regular-field.component';

describe('MultiFieldComponent', () => {
  let component: MultiFieldComponent;
  let fixture: ComponentFixture<MultiFieldComponent>;
  let formBuilder: FormBuilder;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [
        MultiFieldComponent,
        ReactiveFormsModule,
        MatIconModule,
        MatButtonModule,
        MatTooltipModule,
        NoopAnimationsModule,
        RegularFieldComponent
      ]
    })
    .compileComponents();

    fixture = TestBed.createComponent(MultiFieldComponent);
    component = fixture.componentInstance;
    formBuilder = TestBed.inject(FormBuilder);

    // Setup basic test data
    component.field = {
      fieldName: 'testField',
      type: 'string',
      isMulti: true,
      mandatory: false,
      noInput: false
    };

    component.form = formBuilder.group({
      testField: formBuilder.array([
        formBuilder.group({
          testField: ['test value 1']
        }),
        formBuilder.group({
          testField: ['test value 2']
        })
      ])
    });

    component.fields = [];
    component.isViewMode = false;

    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should get multi array correctly', () => {
    const multiArray = component.getMultiArray();
    expect(multiArray).toBeInstanceOf(FormArray);
    expect(multiArray.length).toBe(2);
  });

  it('should create multi field correctly', () => {
    const newField = component.createMultiField();
    expect(newField).toBeInstanceOf(FormGroup);
    expect(newField.get('testField')).toBeTruthy();
  });

  it('should add multi field', () => {
    const initialLength = component.getMultiArray().length;
    component.addMultiField();
    expect(component.getMultiArray().length).toBe(initialLength + 1);
  });

  it('should remove multi field', () => {
    const initialLength = component.getMultiArray().length;
    component.removeMultiField(0);
    expect(component.getMultiArray().length).toBe(initialLength - 1);
  });

  it('should emit field value change', () => {
    spyOn(component.fieldValueChange, 'emit');
    const testEvent = { field: 'test', value: 'test value' };
    
    component.onFieldValueChange(testEvent);
    
    expect(component.fieldValueChange.emit).toHaveBeenCalledWith(testEvent);
  });

  it('should handle read-only state', () => {
    component.field.noInput = true;
    component.ngOnInit();
    
    const multiArray = component.getMultiArray();
    multiArray.controls.forEach(control => {
      const fieldControl = control.get('testField');
      expect(fieldControl?.disabled).toBe(true);
    });
  });

  it('should parse group path correctly', () => {
    component.groupName = 'parentGroup|childGroup';
    const parsed = component['parseGroupPath'](component.groupName);
    expect(parsed.isNested).toBe(true);
    expect(parsed.parent).toBe('parentGroup');
    expect(parsed.child).toBe('childGroup');
  });

  it('should handle grouped multi-fields', () => {
    component.groupName = 'testGroup';
    component.groupIndex = 0;
    
    component.form = formBuilder.group({
      testGroup: formBuilder.array([
        formBuilder.group({
          testField: formBuilder.array([
            formBuilder.group({ testField: ['grouped value'] })
          ])
        })
      ])
    });

    const multiArray = component.getMultiArray();
    expect(multiArray).toBeInstanceOf(FormArray);
    expect(multiArray.length).toBe(1);
  });
});
