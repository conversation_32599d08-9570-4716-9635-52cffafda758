<!-- Regular Field (non-grouped, non-multi) - EXACT from main component lines 54-198 -->
@if (!field.isMulti) {
  <div class="form-field">
    <label [for]="field.fieldName">{{ field.label || field.fieldName }} @if (field.mandatory) {<span>*</span>}
@if (field.noInput) {<span class="no-input-indicator"> (Read Only)</span>}
 </label>

    @if (field.foreginKey) {
      <!-- Check if this is a type field (fieldType) -->
      @if (field.foreginKey === 'fieldType') {
        <div class="dropdown-input-container">
          <input [formControlName]="field.fieldName" [id]="field.fieldName" type="text" 
                 class="form-input dropdown-input" 
                 (input)="onTypeInputChange($event, field.fieldName)" 
                 (focus)="onTypeInputFocus(field.fieldName)" 
                 (blur)="onTypeInputBlur(field.fieldName)"
                 [disabled]="isViewMode || field.noInput"
                 [placeholder]="'Search ' + field.label?.trim() || field.fieldName" />
          
          <!-- Arrow button to toggle dropdown -->
          <button type="button" class="dropdown-arrow-btn" 
                  (click)="toggleTypeDropdown(field.fieldName)" 
                  [disabled]="isViewMode || field.noInput"
                  matTooltip="Show type suggestions">
            <mat-icon>{{ showTypeDropdown[field.fieldName] ? 'keyboard_arrow_up' : 'keyboard_arrow_down' }}</mat-icon>
          </button>
          
          <!-- Dropdown list for filtered results -->
          @if (showTypeDropdown[field.fieldName]) {
            <div class="dropdown-list">
              @if (filteredTypeOptions[field.fieldName] && filteredTypeOptions[field.fieldName].length > 0) {
                @for (option of filteredTypeOptions[field.fieldName]; track option.ROW_ID) {
                  <div class="dropdown-item" (click)="selectTypeOption(option, field.fieldName)">
                    {{ option.ROW_ID }}
                  </div>
                }
              } @else {
                <div class="dropdown-empty">
                  No types found
                </div>
              }
            </div>
          }
        </div>
      }
      <!-- Check if this is a foreign key field (formDefinition) -->
      @else if (field.foreginKey === 'formDefinition') {
        <div class="dropdown-input-container">
          <input [formControlName]="field.fieldName" [id]="field.fieldName" type="text" 
                 class="form-input dropdown-input" 
                 (input)="onForeignKeyInputChange($event, field.fieldName)" 
                 (focus)="onForeignKeyInputFocus(field.fieldName)" 
                 (blur)="onForeignKeyInputBlur(field.fieldName)"
                 [disabled]="isViewMode || field.noInput"
                [placeholder]="'Search ' + (field.label?.trim() || field.fieldName)" />
          
                
          <!-- Arrow button to toggle dropdown -->
          <button type="button" class="dropdown-arrow-btn" 
                  (click)="toggleForeignKeyDropdown(field.fieldName)" 
                  [disabled]="isViewMode || field.noInput"
                  matTooltip="Show foreign key suggestions">
            <mat-icon>{{ showForeignKeyDropdown[field.fieldName] ? 'keyboard_arrow_up' : 'keyboard_arrow_down' }}</mat-icon>
          </button>
          
          <!-- Dropdown list for filtered results -->
          @if (showForeignKeyDropdown[field.fieldName]) {
            <div class="dropdown-list">
              @if (filteredForeignKeyOptions[field.fieldName] && filteredForeignKeyOptions[field.fieldName].length > 0) {
                @for (option of filteredForeignKeyOptions[field.fieldName]; track option.ROW_ID) {
                  <div class="dropdown-item" (click)="selectForeignKeyOption(option, field.fieldName)">
                    {{ option.ROW_ID }}
                  </div>
                }
              } @else {
                <div class="dropdown-empty">
                  No foreign keys found
                </div>
              }
            </div>
          }
        </div>
      }
      <!-- Default dropdown for other foreign keys -->
      @else {
        <div class="dropdown-input-container">
          <input [formControlName]="field.fieldName" [id]="field.fieldName" type="text" 
                 class="form-input dropdown-input" 
                 (input)="onRegularInputChange($event, field.fieldName)" 
                 (focus)="onRegularInputFocus(field.fieldName)" 
                 (blur)="onRegularInputBlur(field.fieldName)"
                 [disabled]="isViewMode || field.noInput"
                 [placeholder]="'Search ' + (field.label?.trim() || field.fieldName)" />
          
          <!-- Arrow button to toggle dropdown -->
          <button type="button" class="dropdown-arrow-btn" 
                  (click)="toggleRegularDropdown(field.fieldName)" 
                  [disabled]="isViewMode || field.noInput"
                  matTooltip="Show options">
            <mat-icon>{{ showRegularDropdown[field.fieldName] ? 'keyboard_arrow_up' : 'keyboard_arrow_down' }}</mat-icon>
          </button>
          
          <!-- Dropdown list for filtered results -->
          @if (showRegularDropdown[field.fieldName]) {
            <div class="dropdown-list">
              @if (filteredRegularOptions[field.fieldName] && filteredRegularOptions[field.fieldName].length > 0) {
                @for (option of filteredRegularOptions[field.fieldName]; track option.ROW_ID) {
                  <div class="dropdown-item" (click)="selectRegularOption(option, field.fieldName)">
                    @for (key of getKeys(option); track key) {
                      {{ option[key] }}&nbsp;
                    }
                  </div>
                }
              } @else {
                <div class="dropdown-empty">
                  No options found
                </div>
              }
            </div>
          }
        </div>
      }
    } @else {
      <!-- Regular input fields for non-foreign key fields -->
      @if (field.type === 'boolean') {
        <input [formControlName]="field.fieldName" [id]="field.fieldName"
          type="checkbox" [readonly]="isViewMode || field.noInput" [disabled]="field.noInput" />
      }
      @if (field.type === 'string') {
        <input [formControlName]="field.fieldName" [id]="field.fieldName"
          type="text" [readonly]="isViewMode || field.noInput" [disabled]="field.noInput" [placeholder]="(field.label?.trim() || field.fieldName)" />
      }
      @if (field.type === 'int') {
        <input [formControlName]="field.fieldName" [id]="field.fieldName"
          type="number" [readonly]="isViewMode || field.noInput" [disabled]="field.noInput" />
      }
      @if (field.type === 'date') {
        <input [formControlName]="field.fieldName" [id]="field.fieldName"
          type="date" [readonly]="isViewMode || field.noInput" [disabled]="field.noInput" />
      }
      @if (field.type === 'double') {
        <input [formControlName]="field.fieldName" [id]="field.fieldName"
          type="number" step="00.50" [readonly]="isViewMode || field.noInput" [disabled]="field.noInput" />
      }
    }
  </div>
}
